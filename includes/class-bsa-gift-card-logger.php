<?php
if (!defined('ABSPATH')) exit;

/**
 * BSA Gift Card Logger
 * 
 * Handles comprehensive logging of gift card operations and remote API activities
 * Following WordPress logging best practices
 * 
 * @since 1.0.0
 */
class BSA_Gift_Card_Logger
{
    /**
     * Logger instance
     * 
     * @var WC_Logger
     */
    protected $logger;

    /**
     * Log contexts
     * 
     * @var array
     */
    protected $context;
    protected $api_context;

    /**
     * Singleton instance
     * 
     * @var BSA_Gift_Card_Logger
     */
    private static $instance = null;

    /**
     * Constructor
     */
    private function __construct()
    {
        $this->logger = wc_get_logger();
        $this->context = ['source' => 'bsa-gift-card'];
        $this->api_context = ['source' => 'bsa-gift-card-api'];

        add_action('woocommerce_add_to_cart', [$this, 'log_gift_card_data'], 10, 6);
    }

    /**
     * Get singleton instance
     * 
     * @return BSA_Gift_Card_Logger
     */
    public static function get_instance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Log gift card data when added to cart
     * 
     * @param string $cart_item_key Cart item key
     * @param int $product_id Product ID
     * @param int $quantity Quantity
     * @param int $variation_id Variation ID
     * @param array $variation Variation data
     * @param array $cart_item_data Cart item data
     */
    public function log_gift_card_data($cart_item_key, $product_id, $quantity, $variation_id, $variation, $cart_item_data)
    {
        $product = wc_get_product($product_id);

        if (!$product || $product->get_type() !== 'bsa-gift-card') {
            return;
        }

        $log_data = [
            'event' => 'gift_card_added_to_cart',
            'timestamp' => current_time('mysql'),
            'cart_item_key' => $cart_item_key,
            'product_id' => $product_id,
            'product_name' => $product->get_name(),
            'quantity' => $quantity,
            'gift_card_data' => [
                'price' => $cart_item_data['gift_card_price'] ?? '',
                'from_name' => $cart_item_data['gift_card_from_name'] ?? '',
                'format' => $cart_item_data['gift_card_format'] ?? '',
                'template' => $cart_item_data['gift_card_template'] ?? '',
                'message' => $cart_item_data['gift_card_message'] ?? '',
            ],
        ];

        // Add recipient based on format
        if (isset($cart_item_data['gift_card_format'])) {
            if ($cart_item_data['gift_card_format'] === 'print' && isset($cart_item_data['gift_card_to_name'])) {
                $log_data['gift_card_data']['to_name'] = $cart_item_data['gift_card_to_name'];
            } elseif ($cart_item_data['gift_card_format'] === 'digital' && isset($cart_item_data['gift_card_to_email'])) {
                $log_data['gift_card_data']['to_email'] = $cart_item_data['gift_card_to_email'];
            }
        }

        $this->log_info('Gift Card Added to Cart', $log_data, $this->context);
    }

    /**
     * Log API connection test results
     * 
     * @param bool $success Whether connection was successful
     * @param string $remote_url Remote URL tested
     * @param string $message Result message
     * @param array $error_data Error data if failed
     */
    public function log_api_connection_test($success, $remote_url, $message, $error_data = [])
    {
        $log_data = [
            'event' => 'api_connection_test',
            'timestamp' => current_time('mysql'),
            'success' => $success,
            'remote_url' => $remote_url,
            'message' => $message
        ];

        if (!$success && !empty($error_data)) {
            $log_data['error_details'] = $error_data;
        }

        $level = $success ? 'info' : 'error';
        $this->log($level, 'API Connection Test', $log_data, $this->api_context);
    }

    /**
     * Log remote coupon creation success
     * 
     * @param string $coupon_code Coupon code created
     * @param float $amount Coupon amount
     * @param array $coupon_data Additional coupon data
     */
    public function log_remote_coupon_success($coupon_code, $amount, $coupon_data = [])
    {
        $log_data = [
            'event' => 'remote_coupon_creation_success',
            'timestamp' => current_time('mysql'),
            'coupon_code' => $coupon_code,
            'coupon_amount' => number_format($amount, 2) . '€',
            'remote_coupon_id' => $coupon_data['remote_id'] ?? '',
            'order_id' => $coupon_data['order_id'] ?? '',
            'api_endpoint' => $this->get_api_endpoint(),
            'created_by' => $this->get_created_by_string()
        ];

        $this->log_notice('Remote Coupon Creation SUCCESS', $log_data, $this->api_context);
    }

    /**
     * Log remote coupon creation failure
     * 
     * @param string $coupon_code Coupon code that failed
     * @param float $amount Coupon amount
     * @param string $error_message Error message
     * @param array $error_data Additional error data
     */
    public function log_remote_coupon_failure($coupon_code, $amount, $error_message, $error_data = [])
    {
        $log_data = [
            'event' => 'remote_coupon_creation_failure',
            'timestamp' => current_time('mysql'),
            'coupon_code' => $coupon_code,
            'coupon_amount' => number_format($amount, 2) . '€',
            'error_message' => $error_message,
            'response_code' => $error_data['response_code'] ?? '',
            'order_id' => $error_data['order_id'] ?? '',
            'api_endpoint' => $this->get_api_endpoint(),
            'created_by' => $this->get_created_by_string()
        ];

        // Include truncated raw response if available
        if (isset($error_data['raw_response'])) {
            $log_data['raw_response'] = substr($error_data['raw_response'], 0, 300) . '...';
        }

        $this->log_error('Remote Coupon Creation FAILED', $log_data, $this->api_context);
    }

    /**
     * Get API endpoint for logging
     * 
     * @return string
     */
    private function get_api_endpoint()
    {
        $api_settings = get_option('bsa_gift_card_api_settings', []);
        $base_url = $api_settings['remote_url'] ?? '';
        return rtrim($base_url, '/') . '/wp-json/wc/v3/coupons';
    }

    /**
     * Get created by string for logging
     * 
     * @return string
     */
    private function get_created_by_string()
    {
        $site_title = get_bloginfo('name');
        $site_description = get_bloginfo('description');
        return $site_title . (!empty($site_description) ? ' - ' . $site_description : '');
    }

    /**
     * Log with specific level (private helper method)
     * 
     * @param string $level Log level
     * @param string $message Message
     * @param array $data Data to log
     * @param array $context Log context
     */
    private function log($level, $message, $data, $context)
    {
        $json_data = wp_json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        $full_message = $message . ': ' . PHP_EOL . $json_data;
        
        $this->logger->{$level}($full_message, $context);
    }

    /**
     * Log info level - PUBLIC so other classes can call it
     * 
     * @param string $message Message
     * @param array $data Data
     * @param array $context Context
     */
    public function log_info($message, $data = [], $context = null)
    {
        $context = $context ?: $this->context;
        $this->log('info', $message, $data, $context);
    }

    /**
     * Log notice level - PUBLIC so other classes can call it
     * 
     * @param string $message Message
     * @param array $data Data
     * @param array $context Context
     */
    public function log_notice($message, $data = [], $context = null)
    {
        $context = $context ?: $this->api_context;
        $this->log('notice', $message, $data, $context);
    }

    /**
     * Log error level - PUBLIC so other classes can call it
     * 
     * @param string $message Message
     * @param array $data Data
     * @param array $context Context
     */
    public function log_error($message, $data = [], $context = null)
    {
        $context = $context ?: $this->context;
        $this->log('error', $message, $data, $context);
    }

    /**
     * Log debug information (only in WP_DEBUG mode)
     * 
     * @param string $message Message
     * @param array $data Data
     * @param array $context Context
     */
    public function log_debug($message, $data = [], $context = null)
    {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $context = $context ?: $this->context;
            $this->log('debug', $message, $data, $context);
        }
    }
}
