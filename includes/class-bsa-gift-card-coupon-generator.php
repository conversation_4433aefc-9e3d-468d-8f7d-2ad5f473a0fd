<?php
if (!defined('ABSPATH')) exit;

/**
 * BSA Gift Card Coupon Generator
 * 
 * Creates WooCommerce coupons via remote API after BSA Gift Card purchase
 * Based on working version with improvements
 * 
 * @since 1.0.0
 */
class BSA_Gift_Card_Coupon_Generator
{
    /**
     * Logger instance
     */
    protected $logger;

    /**
     * API settings
     */
    protected $api_settings;

    /**
     * Constructor
     */
    public function __construct()
    {
        if (class_exists('BSA_Gift_Card_Logger')) {
            $this->logger = BSA_Gift_Card_Logger::get_instance();
        }

        // API settings on each request to ensure fresh data
        add_action('init', [$this, 'refresh_api_settings']);

        // Hook into WooCommerce order processing - after payment
        add_action('woocommerce_order_status_processing', [$this, 'generate_coupons_from_order'], 10, 1);
        add_action('woocommerce_order_status_completed', [$this, 'generate_coupons_from_order'], 10, 1);

        // Add custom meta box to orders
        add_action('add_meta_boxes', [$this, 'add_gift_card_coupon_meta_box']);
    }

    /**
     * Refresh API settings
     */
    public function refresh_api_settings()
    {
        $this->api_settings = get_option('bsa_gift_card_api_settings', []);
    }

    /**
     * Generate coupons from an order containing gift cards
     * 
     * @param int $order_id The order ID
     */
    public function generate_coupons_from_order($order_id)
    {
        $this->refresh_api_settings();

        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }

        // Check if we've already processed this order
        $coupons_generated = $order->get_meta('_bsa_gift_card_coupons_generated');
        if ($coupons_generated === 'yes') {
            return;
        }

        // Check if remote API is configured
        $has_remote_api = $this->is_remote_api_configured();

        if (!$has_remote_api) {
            $order->add_order_note('BSA Gift Card: Remote API nicht konfiguriert - Gutscheine nicht erstellt');
            return;
        }

        $coupons_created = 0;
        $coupons_failed = 0;

        foreach ($order->get_items() as $item_id => $item) {

            if ($item->get_product() && $item->get_product()->get_type() === 'bsa-gift-card') {

                // Get gift card data from order item meta
                $gift_card_price = wc_get_order_item_meta($item_id, 'gift_card_price', true);
                $gift_card_from_name = wc_get_order_item_meta($item_id, 'gift_card_from_name', true);
                $gift_card_format = wc_get_order_item_meta($item_id, 'gift_card_format', true);
                $gift_card_template = wc_get_order_item_meta($item_id, 'gift_card_template', true);
                $gift_card_message = wc_get_order_item_meta($item_id, 'gift_card_message', true);

                // DEBUG: Log the template being retrieved
                error_log('BSA Gift Card: Retrieved template from order meta: ' . ($gift_card_template ?? 'NULL'));

                // Get recipient info based on format
                $recipient_name = '';
                $recipient_email = '';
                if ($gift_card_format === 'print') {
                    $recipient_name = wc_get_order_item_meta($item_id, 'gift_card_to_name', true);
                } elseif ($gift_card_format === 'digital') {
                    $recipient_email = wc_get_order_item_meta($item_id, 'gift_card_to_email', true);
                    $recipient_name = wc_get_order_item_meta($item_id, 'gift_card_to_name', true);
                }

                // Only proceed if we have a valid price
                if (!empty($gift_card_price) && $gift_card_price > 0) {

                    $gift_card_data = [
                        'from_name' => $gift_card_from_name,
                        'recipient_name' => $recipient_name,
                        'recipient_email' => $recipient_email,
                        'format' => $gift_card_format,
                        'template' => $gift_card_template,
                        'message' => $gift_card_message,
                        'order_id' => $order_id,
                        'item_id' => $item_id,
                        'order_date' => $order->get_date_created()->format('Y-m-d H:i:s'),
                        'customer_email' => $order->get_billing_email()
                    ];

                    // DEBUG: Log the gift card data
                    error_log('BSA Gift Card: Gift card data template: ' . ($gift_card_data['template'] ?? 'NULL'));

                    // Generate unique coupon code with DHFPG- prefix
                    $coupon_code = $this->generate_coupon_code();

                    // Create coupon on remote shop via API
                    $remote_coupon_result = $this->create_remote_gift_card_coupon($coupon_code, $gift_card_price, $gift_card_data);

                    // MOVED INSIDE THE LOOP AND SUCCESS CHECK
                    if ($remote_coupon_result && $remote_coupon_result !== false) {
                        wc_add_order_item_meta($item_id, '_gift_card_coupon_id', $remote_coupon_result);
                        wc_add_order_item_meta($item_id, '_gift_card_coupon_code', $coupon_code);

                        // Generate PDF if PDF generator is available - MOVED INSIDE LOOP
                        if (class_exists('BSA_Gift_Card_PDF_Generator')) {
                            error_log('BSA Gift Card: Attempting to generate PDF for coupon: ' . $coupon_code);
                            error_log('BSA Gift Card: Using template: ' . ($gift_card_template ?? 'NULL'));

                            $pdf_generator = new BSA_Gift_Card_PDF_Generator();

                            $pdf_data = array_merge($gift_card_data, [
                                'code' => $coupon_code,
                                'price' => $gift_card_price,
                                'expire_date' => $this->calculate_expiry_date(),
                                'to_name' => $recipient_name,
                                'to_email' => $recipient_email
                            ]);

                            // DEBUG: Final check of template in PDF data
                            error_log('BSA Gift Card: PDF data template final: ' . ($pdf_data['template'] ?? 'NULL'));

                            $pdf_path = $pdf_generator->generate_gift_card_pdf($pdf_data);
                            if ($pdf_path) {
                                wc_add_order_item_meta($item_id, '_gift_card_pdf_path', $pdf_path);
                                error_log('BSA Gift Card: PDF generated successfully: ' . $pdf_path);

                                // Add order note about PDF
                                $order->add_order_note('BSA Gift Card PDF erstellt: ' . basename($pdf_path));
                            } else {
                                error_log('BSA Gift Card: PDF generation failed for coupon: ' . $coupon_code);
                                $order->add_order_note('BSA Gift Card PDF-Generierung fehlgeschlagen für: ' . $coupon_code);
                            }
                        } else {
                            error_log('BSA Gift Card: PDF Generator class not found');
                        }

                        // Add order note
                        $order->add_order_note(sprintf(
                            'BSA Gift Card Gutschein erfolgreich erstellt: %s (Remote ID: %s) für %s€ auf %s',
                            $coupon_code,
                            $remote_coupon_result,
                            number_format($gift_card_price, 2, ',', '.'),
                            $this->api_settings['remote_url']
                        ));

                        $coupons_created++;
                    } else {
                        $coupons_failed++;
                        $order->add_order_note(sprintf(
                            'BSA Gift Card Gutschein-Erstellung fehlgeschlagen: %s für %s€',
                            $coupon_code,
                            number_format($gift_card_price, 2, ',', '.')
                        ));
                    }
                } else {
                    error_log('BSA Gift Card: Invalid price for item ' . $item_id . ': ' . var_export($gift_card_price, true));
                    $coupons_failed++;
                }
            }
        }

        // Add summary order note
        if ($coupons_created > 0 || $coupons_failed > 0) {
            $order->add_order_note(sprintf(
                'BSA Gift Card Verarbeitung abgeschlossen: %d Gutscheine erstellt, %d fehlgeschlagen',
                $coupons_created,
                $coupons_failed
            ));
        }

        // Mark order as processed
        $order->update_meta_data('_bsa_gift_card_coupons_generated', 'yes');
        $order->save();
    }

    /**
     * Check if remote API is configured
     * 
     * @return bool True if remote API is configured
     */
    protected function is_remote_api_configured()
    {
        return !empty($this->api_settings['remote_url']) &&
            !empty($this->api_settings['consumer_key']) &&
            !empty($this->api_settings['consumer_secret']);
    }

    /**
     * Generate unique coupon code with DHFPG prefix
     * 
     * @return string
     */
    protected function generate_coupon_code()
    {
        $random_string = strtoupper(substr(md5(uniqid(wp_rand(), true)), 0, 8));
        return 'DHFPG-' . $random_string;
    }

    /**
     * Calculate expiry date - 3 years from creation, ending on December 31st
     * 
     * @return string Expiry date in Y-m-d format
     */
    protected function calculate_expiry_date()
    {
        $current_year = (int) date('Y');
        $expiry_year = $current_year + 3;
        return $expiry_year . '-12-31';
    }

    /**
     * Build dynamic coupon description
     * 
     * @param array $gift_card_data Gift card data
     * @return string Coupon description
     */
    protected function build_coupon_description($gift_card_data)
    {
        $site_name = get_bloginfo('name');
        return sprintf(
            'BSA Geschenkgutschein - %s Shop - Bestellnummer: %s',
            $site_name,
            $gift_card_data['order_id']
        );
    }

    /**
     * Create coupon on remote shop
     * 
     * @param string $coupon_code Coupon code
     * @param float $amount Coupon amount
     * @param array $gift_card_data Gift card data
     * @return mixed Remote coupon ID or false on failure
     */
    protected function create_remote_gift_card_coupon($coupon_code, $amount, $gift_card_data = [])
    {
        // Validate API settings first
        if (
            empty($this->api_settings['remote_url']) ||
            empty($this->api_settings['consumer_key']) ||
            empty($this->api_settings['consumer_secret'])
        ) {
            error_log('BSA Gift Card: API settings missing for coupon creation');
            return false;
        }

        // validate the remote URL
        $remote_url = rtrim($this->api_settings['remote_url'], '/');

        // validate URL format
        if (!filter_var($remote_url, FILTER_VALIDATE_URL)) {
            error_log('BSA Gift Card: Invalid remote URL format: ' . $remote_url);
            return false;
        }

        // API endpoint URL
        $api_url = $remote_url . '/wp-json/wc/v3/coupons';

        // Calculate expiry date, 3 years from now, ending on December 31st
        $expiry_date = $this->calculate_expiry_date();

        // dynamic description with site name and order number
        $description = $this->build_coupon_description($gift_card_data);

        // coupon data
        $coupon_data = [
            'code' => $coupon_code,
            'discount_type' => 'fixed_cart',
            'amount' => number_format($amount, 2, '.', ''),
            'description' => $description,
            'individual_use' => true,
            'usage_limit' => 1,
            'date_expires' => $expiry_date,
            'meta_data' => [
                [
                    'key' => '_bsa_gift_card_source',
                    'value' => 'DHFPG_Shop'
                ],
                [
                    'key' => '_dhfpg_gift_card_data',
                    'value' => wp_json_encode(array_merge($gift_card_data, [
                        'created_at' => current_time('mysql'),
                        'source_shop' => get_bloginfo('name'),
                        'source_url' => home_url(),
                        'expiry_date' => $expiry_date,
                        'creation_year' => date('Y')
                    ]))
                ],
                [
                    'key' => '_bsa_order_reference',
                    'value' => 'Bestellnummer: ' . $gift_card_data['order_id']
                ],
                [
                    'key' => '_bsa_source_website',
                    'value' => get_bloginfo('name')
                ]
            ]
        ];

        // request
        $request_args = [
            'headers' => [
                'Authorization' => 'Basic ' . base64_encode($this->api_settings['consumer_key'] . ':' . $this->api_settings['consumer_secret']),
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ],
            'body' => wp_json_encode($coupon_data),
            'timeout' => 30,
            'sslverify' => false
        ];

        // API request
        $response = wp_remote_post($api_url, $request_args);

        // response
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log('BSA Gift Card: WP Error: ' . $error_message);

            if ($this->logger) {
                $this->logger->log_remote_coupon_failure($coupon_code, $amount, $error_message, [
                    'api_url' => $api_url,
                    'order_id' => $gift_card_data['order_id'] ?? ''
                ]);
            }
            return false;
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = wp_remote_retrieve_body($response);

        if ($response_code === 201) {
            // success - coupon created
            $response_data = json_decode($response_body, true);

            if (isset($response_data['id']) && $response_data['id']) {
                if ($this->logger) {
                    $this->logger->log_remote_coupon_success($coupon_code, $amount, [
                        'remote_id' => $response_data['id'],
                        'order_id' => $gift_card_data['order_id'] ?? '',
                        'expiry_date' => $expiry_date,
                        'source_website' => get_bloginfo('name')
                    ]);
                }
                return $response_data['id'];
            }
        }

        // Only log errors
        $error_message = 'HTTP ' . $response_code;
        $response_data = json_decode($response_body, true);
        if (isset($response_data['message'])) {
            $error_message .= ': ' . $response_data['message'];
        }

        error_log('BSA Gift Card: API Error: ' . $error_message);

        if ($this->logger) {
            $this->logger->log_remote_coupon_failure($coupon_code, $amount, $error_message, [
                'response_code' => $response_code,
                'api_url' => $api_url,
                'order_id' => $gift_card_data['order_id'] ?? '',
                'raw_response' => substr($response_body, 0, 300)
            ]);
        }

        return false;
    }

    /**
     * Add meta box to orders screen
     */
    public function add_gift_card_coupon_meta_box()
    {
        add_meta_box(
            'bsa_gift_card_coupons',
            __('BSA Gift Card Gutscheine', 'bsa-gift-card'),
            [$this, 'render_coupon_meta_box'],
            'shop_order',
            'side',
            'high'
        );
    }

    /**
     * Render the gift card coupon meta box
     * 
     * @param WP_Post $post
     */
    public function render_coupon_meta_box($post)
    {
        $order = wc_get_order($post->ID);
        if (!$order) {
            return;
        }

        $is_remote_mode = $this->is_remote_api_configured();

        echo '<div style="margin-bottom: 10px; padding: 10px; background: #f9f9f9; border-left: 3px solid #007cba;">';
        if ($is_remote_mode) {
            echo '<p><strong>Status:</strong> <span style="color: #46b450;">✓ Remote API konfiguriert</span></p>';
            echo '<p><strong>Ziel-Shop:</strong> ' . esc_html($this->api_settings['remote_url']) . '</p>';
        } else {
            echo '<p><strong>Status:</strong> <span style="color: #d63638;">✗ Remote API nicht konfiguriert</span></p>';
            echo '<p><em>Gutscheine können nicht erstellt werden</em></p>';
        }
        echo '</div>';

        $has_gift_cards = false;

        foreach ($order->get_items() as $item_id => $item) {
            if ($item->get_product() && $item->get_product()->get_type() === 'bsa-gift-card') {
                $has_gift_cards = true;

                $coupon_code = wc_get_order_item_meta($item_id, '_gift_card_coupon_code', true);
                $coupon_id = wc_get_order_item_meta($item_id, '_gift_card_coupon_id', true);
                $gift_card_price = wc_get_order_item_meta($item_id, 'gift_card_price', true);
                $gift_card_format = wc_get_order_item_meta($item_id, 'gift_card_format', true);

                echo '<div style="margin-bottom: 15px; padding: 10px; background: #f9f9f9; border-left: 3px solid #007cba;">';
                echo '<h4 style="margin: 0 0 10px 0;">Geschenkgutschein (' . wc_price($gift_card_price) . ')</h4>';

                if (!empty($coupon_code) && !empty($coupon_id)) {
                    echo '<p><strong>Gutschein-Code:</strong> <code style="background: #fff; padding: 2px 4px; font-size: 14px;">' . esc_html($coupon_code) . '</code></p>';
                    echo '<p><strong>Remote Coupon ID:</strong> ' . esc_html($coupon_id) . '</p>';
                    echo '<p><strong>Status:</strong> <span style="color: #46b450;">✓ Erfolgreich erstellt</span></p>';

                    $expiry_date = $this->calculate_expiry_date();
                    echo '<p><strong>Gültig bis:</strong> ' . date('d.m.Y', strtotime($expiry_date)) . '</p>';
                    echo '<p><strong>Erstellt von:</strong> ' . esc_html(get_bloginfo('name')) . ' Shop</p>';

                    if ($gift_card_format === 'print') {
                        $recipient = wc_get_order_item_meta($item_id, 'gift_card_to_name', true);
                        echo '<p><strong>Empfänger:</strong> ' . esc_html($recipient) . ' (Druck)</p>';
                    } elseif ($gift_card_format === 'digital') {
                        $recipient = wc_get_order_item_meta($item_id, 'gift_card_to_email', true);
                        echo '<p><strong>Empfänger:</strong> ' . esc_html($recipient) . ' (Digital)</p>';
                    }
                } else {
                    echo '<p><strong>Status:</strong> <span style="color: #d63638;">✗ Gutschein nicht erstellt</span></p>';
                    if (!$is_remote_mode) {
                        echo '<p><small>Grund: Remote API nicht konfiguriert</small></p>';
                    } else {
                        echo '<p><small>Grund: Erstellung fehlgeschlagen oder noch nicht verarbeitet</small></p>';
                    }
                }
                echo '</div>';
            }
        }

        if (!$has_gift_cards) {
            echo '<p style="color: #666; font-style: italic;">Keine Geschenkgutscheine in dieser Bestellung.</p>';
        }
    }
}
