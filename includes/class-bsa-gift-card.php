<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BSA Gift Card Main Class
 * 
 * Handles BSA Gift Card product type with Elementor shortcode rendering only
 * 
 * @since 1.0.0
 */
class BSA_Gift_Card
{
    /**
     * Plugin constants
     */
    private const MIN_PRICE = 10;

    /**
     * Initialize the custom product registration
     */
    public function init()
    {
        // Core product type registration
        add_action('init', [$this, 'register_gift_product_type']);
        add_filter('product_type_selector', [$this, 'add_gift_product_type_selector']);

        // ADMIN: Control which tabs are visible for BSA Gift Card products
        add_filter('woocommerce_product_data_tabs', [$this, 'custom_product_data_tabs'], 10, 1);
        // Add custom tab panels
        add_action('woocommerce_product_data_panels', [$this, 'add_custom_product_data_panels']);

        // IMPORTANT: Add admin footer script for tab visibility (like PW Gift Cards does)
        add_action('admin_footer', [$this, 'admin_footer']);

        // Cart and checkout functionality
        add_filter('woocommerce_add_cart_item_data', [$this, 'add_custom_price_cart_item_data'], 10, 3);
        add_action('woocommerce_before_calculate_totals', [$this, 'update_cart_price']);
        add_action('woocommerce_before_calculate_totals', [$this, 'apply_virtual_status'], 20);
        add_filter('woocommerce_get_item_data', [$this, 'display_gift_product_data_in_cart'], 10, 2);

        // Validation
        add_filter('woocommerce_add_to_cart_validation', [$this, 'validate_gift_card_purchase'], 10, 3);

        // Order processing
        add_filter('woocommerce_cart_item_name', [$this, 'remove_short_description_from_cart'], 10, 3);
        add_action('woocommerce_checkout_create_order_line_item', [$this, 'save_gift_card_data_to_order_item'], 10, 4);

        // Admin functionality
        if (is_admin()) {
            add_action('woocommerce_process_product_meta_bsa-gift-card', [$this, 'setup_gift_card_product'], 10, 1);
            add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
            add_action('woocommerce_product_options_general_product_data', [$this, 'add_custom_product_fields']);
            add_action('woocommerce_process_product_meta', [$this, 'save_custom_product_fields']);
            add_action('admin_enqueue_scripts', [$this, 'admin_enqueue_scripts']);
            add_action('admin_head', [$this, 'admin_print_styles']);
        }

        // Shortcode registration
        add_shortcode('bsa_gift_card_form', [$this, 'render_gift_card_form_shortcode']);

        // PDF download handler
        add_action('wp_ajax_download_gift_card_pdf', [$this, 'handle_pdf_download']);
        add_action('wp_ajax_nopriv_download_gift_card_pdf', [$this, 'handle_pdf_download']);

        // Initialize meta labels
        $this->format_order_item_meta_labels();
    }

    /**
     * Add JavaScript to handle product tab visibility in admin footer (like PW Gift Cards)
     */
    public function admin_footer()
    {
        if ('product' != get_post_type()) {
            return;
        }
?>
        <script type='text/javascript'>
            jQuery(document).ready(function($) {
                // Add show classes to tabs that should be visible for BSA Gift Cards
                $('.inventory_options').addClass('show_if_bsa-gift-card');
                $('#inventory_product_data ._sold_individually_field').parent().addClass('show_if_bsa-gift-card');
                $('#inventory_product_data ._sold_individually_field').addClass('show_if_bsa-gift-card');
                $('#general_product_data ._tax_status_field').closest('.options_group').addClass('show_if_bsa-gift-card');

                // Function to handle BSA Gift Card tab visibility
                function handleBSAGiftCardTabs() {
                    var productType = $('#product-type').val();

                    if (productType === 'bsa-gift-card') {
                        // Show required tabs for BSA Gift Cards
                        $('.general_options.general_tab').show();
                        $('.shipping_options.shipping_tab').show();
                        $('.inventory_options.inventory_tab').show();
                        $('.bsa_gift_card_settings_options').show();

                        // Hide unnecessary tabs
                        $('.linked_product_options').hide();
                        $('.attribute_options').hide();
                        $('.variations_options').hide();

                        // Show specific fields within tabs
                        $('.show_if_bsa-gift-card').show();
                        $('.hide_if_bsa-gift-card').hide();

                        // NO DEFAULT CHECKBOX SETTING - LET WOOCOMMERCE HANDLE IT

                    } else {
                        // Hide BSA Gift Card specific tabs for other product types
                        $('.bsa_gift_card_settings_options').hide();
                    }
                }

                // Run on page load
                setTimeout(handleBSAGiftCardTabs, 100);

                // Run when product type changes
                $('#product-type').on('change', function() {
                    setTimeout(handleBSAGiftCardTabs, 100);
                });

                // Also listen for WooCommerce events
                $(document).on('woocommerce_product_type_change', function() {
                    setTimeout(handleBSAGiftCardTabs, 100);
                });
            });
        </script>
    <?php
    }




    /**
     * Enqueue admin assets
     */
    public function enqueue_admin_assets($hook)
    {
        global $post;

        // Only load on product edit pages
        if ($hook !== 'post.php' && $hook !== 'post-new.php') {
            return;
        }

        if (!$post || $post->post_type !== 'product') {
            return;
        }

        // Enqueue admin CSS for custom icon
        wp_enqueue_style(
            'bsa-gift-card-admin',
            BSA_GIFT_CARD_PLUGIN_URL . 'assets/css/bsa-gift-card-admin.css',
            [],
            BSA_GIFT_CARD_VERSION
        );
    }

    /**
     * ADMIN: Control which tabs are visible for BSA Gift Card products
     * 
     * @param array $tabs Existing product data tabs
     * @return array Modified tabs
     */
    public function custom_product_data_tabs($tabs)
    {
        // Make General tab visible for BSA Gift Card products
        if (isset($tabs['general'])) {
            if (!isset($tabs['general']['class']) || !is_array($tabs['general']['class'])) {
                $tabs['general']['class'] = [];
            }

            // Remove any hide classes and add show class
            $tabs['general']['class'] = array_diff($tabs['general']['class'], ['hide_if_bsa-gift-card']);
            if (!in_array('show_if_bsa-gift-card', $tabs['general']['class'])) {
                $tabs['general']['class'][] = 'show_if_bsa-gift-card';
            }
        }

        // Shipping tab visible for BSA Gift Card products 
        if (isset($tabs['shipping'])) {
            if (!isset($tabs['shipping']['class']) || !is_array($tabs['shipping']['class'])) {
                $tabs['shipping']['class'] = [];
            }

            // Remove any hide classes and add show class
            $tabs['shipping']['class'] = array_diff($tabs['shipping']['class'], ['hide_if_bsa-gift-card']);
            if (!in_array('show_if_bsa-gift-card', $tabs['shipping']['class'])) {
                $tabs['shipping']['class'][] = 'show_if_bsa-gift-card';
            }
        }

        // Make inventory tab visible for BSA Gift Card products
        if (isset($tabs['inventory'])) {
            if (!isset($tabs['inventory']['class']) || !is_array($tabs['inventory']['class'])) {
                $tabs['inventory']['class'] = [];
            }
            $tabs['inventory']['class'][] = 'show_if_bsa-gift-card';
        }

        // ADD YOUR CUSTOM TAB HERE
        $tabs['bsa_gift_card_settings'] = [
            'label'    => __('BSA Gift Card', 'bsa-gift-card'),
            'target'   => 'bsa_gift_card_settings_panel',
            'class'    => ['show_if_bsa-gift-card', 'bsa-gift-card-tab'],
            'priority' => 5,
        ];

        // hide tabs
        $tabs_to_hide = ['linked_product', 'attribute', 'variations'];
        foreach ($tabs_to_hide as $tab) {
            if (isset($tabs[$tab])) {
                if (!isset($tabs[$tab]['class']) || !is_array($tabs[$tab]['class'])) {
                    $tabs[$tab]['class'] = [];
                }
                $tabs[$tab]['class'][] = 'hide_if_bsa-gift-card';
            }
        }

        return $tabs;
    }

    /**
     * Add custom product data panels for BSA Gift Card
     */
    public function add_custom_product_data_panels()
    {
        global $post;
        $product = wc_get_product($post->ID);

        // Only show for BSA gift card products
        if (!$product || $product->get_type() !== 'bsa-gift-card') {
            return;
        }

        // Gift Card Settings Panel
    ?>
        <div id="bsa_gift_card_settings_panel" class="panel woocommerce_options_panel">
            <div class='options_group show_if_bsa-gift-card'>
                <div style="padding: 32px;">
                    <div style="font-weight: 600; font-size: 125%;">BSA Gift Cards </div>
                    <ul style="padding: 8px 32px;">
                        <li><strong>Custom Betrag</strong> - Der Kunde gibt den eigenen Betrag ein.</li>
                        <li><strong>4 Designs</strong> - Der Kunde kann zwischen vier verschiedenen Motiven auswählen. Geschenk, Geburtstag, Ostern und Weihnachten.</li>
                        <li><strong>Digital oder Print</strong> - Der Kunde kann zwischen einer digitalen (PDF) und einer Print-Version (ausgedruckt) wählen.</li>
                    </ul>
                </div>
            </div>
        </div>
    <?php
    }

    /**
     * Add custom product fields for BSA Gift Card type
     */
    public function add_custom_product_fields()
    {
        global $post;

        echo '<div class="options_group show_if_bsa-gift-card">';

        // Virtual checkbox (for digital delivery)
        woocommerce_wp_checkbox([
            'id' => '_virtual',
            'label' => __('Virtual', 'woocommerce'),
            'description' => __('Virtual products are intangible and are not shipped.', 'woocommerce'),
            'desc_tip' => true
        ]);

        // Downloadable checkbox (for PDF generation)
        woocommerce_wp_checkbox([
            'id' => '_downloadable',
            'label' => __('Downloadable', 'woocommerce'),
            'description' => __('Downloadable products give access to a file upon purchase.', 'woocommerce'),
            'desc_tip' => true
        ]);

        echo '</div>';
    }


    /**
     * Enhanced validation for BSA Gift Card purchase
     *
     * @param bool $valid Current validation status
     * @param int $product_id Product ID being added to cart
     * @param int $quantity Quantity being added
     * @return bool Whether the product can be added to cart
     */
    public function validate_gift_card_purchase($valid, $product_id, $quantity)
    {
        $product = wc_get_product($product_id);

        if (!$product || $product->get_type() !== 'bsa-gift-card') {
            return $valid;
        }

        // validate minimum price
        if (isset($_POST['gift_card_price']) && $_POST['gift_card_price'] < self::MIN_PRICE) {
            wc_add_notice(
                sprintf(__('Bitte geben Sie einen Geschenkgutschein-Wert von mindestens %s ein.', 'bsa-gift-card'), wc_price(self::MIN_PRICE)),
                'error'
            );
            return false;
        }

        // validate format selection
        if (!isset($_POST['gift_card_format']) || empty($_POST['gift_card_format'])) {
            wc_add_notice(__('Bitte wählen Sie ein Format aus (Print oder Digital).', 'bsa-gift-card'), 'error');
            return false;
        }

        // validate format-specific fields
        $format = sanitize_text_field($_POST['gift_card_format']);
        if ($format === 'print' && (empty($_POST['gift_card_to_name']) || trim($_POST['gift_card_to_name']) === '')) {
            wc_add_notice(__('Bitte geben Sie den Empfängernamen für den Druck ein.', 'bsa-gift-card'), 'error');
            return false;
        }

        if ($format === 'digital' && (empty($_POST['gift_card_to_email']) || trim($_POST['gift_card_to_email']) === '')) {
            wc_add_notice(__('Bitte geben Sie die E-Mail-Adresse für den digitalen Versand ein.', 'bsa-gift-card'), 'error');
            return false;
        }

        return $valid;
    }

    /**
     * Render gift card form as shortcode (Elementor compatibility)
     *
     * @return string Rendered HTML
     */
    public function render_gift_card_form_shortcode()
    {
        global $product;

        if (!$product || $product->get_type() !== 'bsa-gift-card') {
            return '';
        }

        $this->enqueue_shortcode_assets();

        ob_start();
        include_once BSA_GIFT_PRODUCT_PATH . 'templates/single-product/add-to-cart/bsa-gift-card.php';
        return ob_get_clean();
    }

    /**
     * Enqueue assets for shortcode usage
     */
    private function enqueue_shortcode_assets()
    {
        wp_enqueue_script(
            'bsa-gift-card-frontend',
            BSA_GIFT_PRODUCT_URL . 'assets/js/bsa-gift-card.js',
            ['jquery'],
            BSA_GIFT_CARD_VERSION,
            true
        );

        wp_localize_script('bsa-gift-card-frontend', 'bsaGiftCard', [
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('bsa-gift-card-nonce'),
            'debug' => defined('WP_DEBUG') && WP_DEBUG
        ]);

        if (!wp_style_is('gift-card-style', 'enqueued')) {
            wp_enqueue_style(
                'gift-card-style',
                BSA_GIFT_PRODUCT_URL . 'assets/css/bsa-gift-card.css',
                [],
                BSA_GIFT_CARD_VERSION
            );
        }

        add_action('wp_footer', [$this, 'add_inline_init_script'], 20);
    }

    /**
     * Add inline initialization script
     */
    public function add_inline_init_script()
    {
        static $script_added = false;
        if ($script_added) {
            return;
        }
        $script_added = true;

    ?>
        <script type="text/javascript">
            jQuery(document).ready(function($) {
                if (typeof window.reinitBSAGiftCard === 'function') {
                    setTimeout(function() {
                        window.reinitBSAGiftCard();
                    }, 200);
                }
            });
        </script>
        <?php
    }

    /**
     * Setup gift card product with correct default settings and save custom fields
     * 
     * @param int $post_id Product post ID
     */
    public function setup_gift_card_product($post_id)
    {
        if (!current_user_can('edit_product', $post_id)) {
            return;
        }

        $product = wc_get_product($post_id);

        if (!$product || $product->get_type() !== 'bsa-gift-card') {
            return;
        }

        // Set default properties for BSA Gift Cards (excluding virtual/downloadable)
        $product->set_catalog_visibility('visible');
        $product->set_manage_stock(false);
        $product->set_stock_status('instock');
        $product->set_sold_individually(true);

        // DO NOT FORCE VIRTUAL OR DOWNLOADABLE - RESPECT USER CHECKBOX CHOICES
        // Let WooCommerce handle these checkboxes naturally

        if (!$product->get_price()) {
            $product->set_price(self::MIN_PRICE);
            $product->set_regular_price(self::MIN_PRICE);
        }

        if ($product->get_status() === 'draft') {
            $product->set_status('publish');
        }

        $product->save();

        // Save custom fields from Gift Card Settings tab
        $custom_fields = [
            '_bsa_min_price',
            '_bsa_max_price',
            '_bsa_valid_days',
            '_bsa_enable_digital',
            '_bsa_enable_print',
            '_bsa_custom_template'
        ];

        foreach ($custom_fields as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, $field, sanitize_text_field($_POST[$field]));
            }
        }

        // Save template selections
        $all_templates = ['template1', 'template2', 'template3', 'template4'];
        $selected_templates = [];

        foreach ($all_templates as $template) {
            if (isset($_POST["_bsa_template_{$template}"]) && $_POST["_bsa_template_{$template}"] === 'yes') {
                $selected_templates[] = $template;
            }
        }

        update_post_meta($post_id, '_bsa_available_templates', $selected_templates);
    }


    /**
     * Register custom product type class
     */
    public function register_gift_product_type()
    {
        $file_path = BSA_GIFT_PRODUCT_PATH . 'includes/class-wc-product-bsa-gift-card.php';
        if (file_exists($file_path)) {
            require_once $file_path;
        }
    }

    /**
     * Add custom product type to selector
     * 
     * @param array $types Existing product types
     * @return array Modified product types
     */
    public function add_gift_product_type_selector($types)
    {
        $types['bsa-gift-card'] = __('BSA Gift Card', 'bsa-gift-card');
        return $types;
    }

    /**
     * Capture custom gift data when adding to cart
     * 
     * @param array $cart_item_data Existing cart item data
     * @param int $product_id Product ID
     * @param int $variation_id Variation ID
     * @return array Modified cart item data
     */
    public function add_custom_price_cart_item_data($cart_item_data, $product_id, $variation_id = 0)
    {
        $product = wc_get_product($product_id);

        if (!$product || $product->get_type() !== 'bsa-gift-card') {
            return $cart_item_data;
        }

        $fields = [
            'gift_card_price' => 'float',
            'gift_card_from_name' => 'text',
            'gift_card_to_name' => 'text',
            'gift_card_to_email' => 'email',
            'gift_card_template' => 'text',
            'gift_card_format' => 'text',
            'gift_card_message' => 'textarea'
        ];

        foreach ($fields as $field => $type) {
            if (isset($_POST[$field])) {
                switch ($type) {
                    case 'float':
                        $cart_item_data[$field] = (float) $_POST[$field];
                        break;
                    case 'email':
                        $cart_item_data[$field] = sanitize_email($_POST[$field]);
                        break;
                    case 'textarea':
                        $cart_item_data[$field] = sanitize_textarea_field($_POST[$field]);
                        break;
                    default:
                        $cart_item_data[$field] = sanitize_text_field($_POST[$field]);
                }
            }
        }

        // set virtual status based on format
        if (isset($cart_item_data['gift_card_format'])) {
            $cart_item_data['is_virtual'] = ($cart_item_data['gift_card_format'] === 'digital');
        }

        // unique key to prevent item merging
        $cart_item_data['unique_key'] = wp_generate_uuid4();

        return $cart_item_data;
    }

    /**
     * Apply virtual status based on format selection
     * 
     * @param WC_Cart $cart_obj Cart object
     */
    public function apply_virtual_status($cart_obj)
    {
        if (is_admin() && !wp_doing_ajax()) {
            return;
        }

        foreach ($cart_obj->get_cart() as $cart_item) {
            if (
                $cart_item['data']->get_type() === 'bsa-gift-card' &&
                isset($cart_item['gift_card_format'])
            ) {

                $format = $cart_item['gift_card_format'];

                if ($format === 'digital') {
                    $cart_item['data']->set_virtual(true);
                    $cart_item['data']->set_weight(0);
                } elseif ($format === 'print') {
                    $cart_item['data']->set_virtual(false);
                    $cart_item['data']->set_weight(0.1);
                }
            }
        }
    }

    /**
     * Update cart item prices
     * 
     * @param WC_Cart $cart_obj Cart object
     */
    public function update_cart_price($cart_obj)
    {
        if (is_admin() && !wp_doing_ajax()) {
            return;
        }

        foreach ($cart_obj->get_cart() as $item) {
            if (isset($item['gift_card_price']) && $item['gift_card_price'] > 0) {
                $item['data']->set_price($item['gift_card_price']);
            }
        }
    }

    /**
     * Display gift card data in cart
     * 
     * @param array $item_data Existing item data
     * @param array $cart_item Cart item
     * @return array Modified item data
     */
    public function display_gift_product_data_in_cart($item_data, $cart_item)
    {
        $display_fields = [
            'gift_card_price' => __('Geschenkgutschein-Wert', 'bsa-gift-card'),
            'gift_card_from_name' => __('Von', 'bsa-gift-card'),
            'gift_card_to_name' => __('Für', 'bsa-gift-card'),
            'gift_card_to_email' => __('Empfänger E-Mail', 'bsa-gift-card'),
            'gift_card_template' => __('Design', 'bsa-gift-card'),
            'gift_card_format' => __('Format', 'bsa-gift-card'),
            'gift_card_message' => __('Nachricht', 'bsa-gift-card')
        ];

        foreach ($display_fields as $field => $label) {
            if (isset($cart_item[$field])) {
                $value = $field === 'gift_card_price' ? wc_price($cart_item[$field]) : $cart_item[$field];
                $item_data[] = [
                    'key' => $label,
                    'value' => $value
                ];
            }
        }

        return $item_data;
    }

    /**
     * Remove short description from cart items
     * 
     * @param string $product_name Product name
     * @param array $cart_item Cart item
     * @param string $cart_item_key Cart item key
     * @return string Modified product name
     */
    public function remove_short_description_from_cart($product_name, $cart_item, $cart_item_key)
    {
        if (is_cart() || is_checkout()) {
            $product_name = preg_replace('/<div class="woocommerce-product-details__short-description">.*?<\/div>/s', '', $product_name);
        }
        return $product_name;
    }

    /**
     * Format order item meta labels
     */
    public function format_order_item_meta_labels()
    {
        add_filter('woocommerce_order_item_display_meta_key', [$this, 'format_gift_card_meta_key'], 10, 3);
        add_filter('woocommerce_order_item_display_meta_value', [$this, 'format_pdf_display'], 10, 3);
    }

    /**
     * Format gift card meta keys for display
     */
    public function format_gift_card_meta_key($display_key, $meta, $item)
    {
        $key_map = [
            'gift_card_price' => __('Betrag', 'bsa-gift-card'),
            'gift_card_from_name' => __('Von', 'bsa-gift-card'),
            'gift_card_to_name' => __('Für', 'bsa-gift-card'),
            'gift_card_to_email' => __('Empfänger E-Mail', 'bsa-gift-card'),
            'gift_card_template' => __('Motiv', 'bsa-gift-card'),
            'gift_card_format' => __('Format', 'bsa-gift-card'),
            'gift_card_message' => __('Nachricht', 'bsa-gift-card'),
            '_gift_card_coupon_code' => __('Gutschein-Code', 'bsa-gift-card'),
            '_gift_card_coupon_id' => __('Remote Coupon ID', 'bsa-gift-card'),
            '_gift_card_pdf_path' => __('PDF-Datei', 'bsa-gift-card'),
        ];

        return isset($key_map[$meta->key]) ? $key_map[$meta->key] : $display_key;
    }

    /**
     * Handle secure PDF download
     */
    public function handle_pdf_download()
    {
        // Verify nonce
        if (!isset($_GET['nonce']) || !wp_verify_nonce($_GET['nonce'], 'gift_card_pdf_download')) {
            wp_die('Security check failed');
        }

        $order_id = intval($_GET['order_id']);
        $item_id = intval($_GET['item_id']);

        // Get order
        $order = wc_get_order($order_id);
        if (!$order) {
            wp_die('Order not found');
        }

        // Check permissions - admin or order customer
        if (!current_user_can('edit_shop_orders') && $order->get_customer_id() !== get_current_user_id()) {
            wp_die('Access denied');
        }

        // Get PDF path
        $pdf_path = wc_get_order_item_meta($item_id, '_gift_card_pdf_path', true);

        if (!$pdf_path || !file_exists($pdf_path)) {
            wp_die('PDF file not found');
        }

        // Serve the file
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="gift-card-' . $order_id . '-' . $item_id . '.pdf"');
        header('Content-Length: ' . filesize($pdf_path));
        readfile($pdf_path);
        exit;
    }

    /**
     * Format PDF path display with secure download button
     */
    public function format_pdf_display($display_value, $meta, $item)
    {
        if ($meta->key === '_gift_card_pdf_path' && !empty($meta->value)) {
            // Check if file exists
            if (!file_exists($meta->value)) {
                return '<span style="color: #d63638;">PDF nicht gefunden</span>';
            }

            $order_id = $item->get_order_id();
            $item_id = $item->get_id();
            $nonce = wp_create_nonce('gift_card_pdf_download');

            $download_url = admin_url('admin-ajax.php') . '?' . http_build_query([
                'action' => 'download_gift_card_pdf',
                'order_id' => $order_id,
                'item_id' => $item_id,
                'nonce' => $nonce
            ]);

            return '<a href="' . esc_url($download_url) . '" target="_blank" class="button button-small" style="background: #0073aa; color: white; text-decoration: none; padding: 5px 10px; border-radius: 3px;">PDF anzeigen</a>';
        }
        return $display_value;
    }

    /**
     * Save gift card data to order item meta
     * 
     * @param WC_Order_Item_Product $item Order item
     * @param string $cart_item_key Cart item key
     * @param array $values Cart item values
     * @param WC_Order $order Order object
     */
    public function save_gift_card_data_to_order_item($item, $cart_item_key, $values, $order)
    {
        $fields = [
            'gift_card_price',
            'gift_card_from_name',
            'gift_card_to_name',
            'gift_card_to_email',
            'gift_card_template',
            'gift_card_format',
            'gift_card_message',
            '_gift_card_coupon_id',
        ];

        foreach ($fields as $field) {
            if (isset($values[$field])) {
                $item->add_meta_data($field, $values[$field]);
            }
        }
    }

    /**
     * Save custom product field values
     */
    public function save_custom_product_fields($post_id)
    {
        $product = wc_get_product($post_id);

        if (!$product || $product->get_type() !== 'bsa-gift-card') {
            return;
        }

        // Save virtual status
        $virtual = isset($_POST['_virtual']) ? 'yes' : 'no';
        $product->set_virtual('yes' === $virtual);

        // Save downloadable status  
        $downloadable = isset($_POST['_downloadable']) ? 'yes' : 'no';
        $product->set_downloadable('yes' === $downloadable);

        $product->save();
    }

    /**
     * Add admin script to handle custom product type tab and field visibility
     */
    public function admin_enqueue_scripts($hook)
    {
        if (('post.php' === $hook || 'post-new.php' === $hook) && get_post_type() === 'product') {
            wp_add_inline_script('wc-admin-product-meta-boxes', '
            jQuery(document).ready(function($) {
                // Function to control BSA Gift Card visibility
                function controlBSAGiftCardFields() {
                    var productType = $("#product-type").val();
                    var isVirtual = $("#_virtual").is(":checked");
                    var isDownloadable = $("#_downloadable").is(":checked");
                    
                    if (productType === "bsa-gift-card") {
                        console.log("BSA Gift Card selected - controlling fields");
                        
                        // Show General and Shipping tabs
                        $(".general_options, .shipping_options").show().css("display", "block");
                        
                        // Show only our custom checkboxes
                        $(".show_if_bsa-gift-card").show();
                        
                        // Hide unwanted fields
                        $(".pricing, .inventory_options, .linked_product_options, .attribute_options, .variations_options, .advanced_options").hide();
                        
                        // Show/hide shipping fields based on virtual status
                        if (isVirtual) {
                            $(".shipping_options .hide_if_virtual").hide();
                            $(".shipping_tab").hide();
                        } else {
                            $(".shipping_options .hide_if_virtual").show(); 
                            $(".shipping_tab").show();
                        }
                        
                        // Show/hide download fields based on downloadable status
                        if (isDownloadable) {
                            $(".show_if_downloadable").show();
                        } else {
                            $(".show_if_downloadable").hide();
                        }
                        
                        // Activate General tab if none active
                        if (!$(".product_data_tabs li.active:visible").length) {
                            $(".product_data_tabs li").removeClass("active");
                            $(".general_options").addClass("active");
                            $(".panel").hide();
                            $("#general_product_data").show();
                        }
                    }
                }
                
                // Trigger on product type change
                $("#product-type").on("change", controlBSAGiftCardFields);
                
                // Trigger on checkbox changes
                $("#_virtual, #_downloadable").on("change", controlBSAGiftCardFields);
                
                // Initial load
                controlBSAGiftCardFields();
                
                // Multiple triggers to ensure it works
                setTimeout(controlBSAGiftCardFields, 100);
                setTimeout(controlBSAGiftCardFields, 500);
            });
        ');
        }
    }

    /**
     * Add admin CSS for BSA Gift Card product type
     */
    public function admin_print_styles()
    {
        if ((isset($_GET['post_type']) && $_GET['post_type'] === 'product') ||
            (isset($_GET['post']) && get_post_type($_GET['post']) === 'product')
        ) {
        ?>
            <style>
                /* Show BSA Gift Card specific fields */
                .show_if_bsa-gift-card {
                    display: block !important;
                }

                /* Hide fields not needed for BSA Gift Card */
                [data-product-type="bsa-gift-card"] .pricing,
                [data-product-type="bsa-gift-card"] .inventory_options,
                [data-product-type="bsa-gift-card"] .linked_product_options,
                [data-product-type="bsa-gift-card"] .attribute_options,
                [data-product-type="bsa-gift-card"] .variations_options,
                [data-product-type="bsa-gift-card"] .advanced_options {
                    display: none !important;
                }

                /* Force visibility for required tabs */
                body.post-type-product .general_options.show_if_bsa-gift-card,
                body.post-type-product .shipping_options.show_if_bsa-gift-card {
                    display: block !important;
                }
            </style>
<?php
        }
    }
}
