<?php
if (!defined('ABSPATH')) {
    exit;
}

/**
 * BSA Gift Card Product Class
 * 
 * Defines the BSA Gift Card product type behavior
 * 
 * @since 1.0.0
 */
class WC_Product_BSA_Gift_Card extends WC_Product
{
    /**
     * Product type
     * 
     * @var string
     */
    public $product_type = 'bsa-gift-card';

    /**
     * Constructor
     * 
     * @param mixed $product Product data
     */
    public function __construct($product = 0)
    {
        parent::__construct($product);
        $this->product_type = 'bsa-gift-card';

        // Set default properties
        $this->set_default_properties();
    }

    /**
     * Set default properties for gift cards
     */
    protected function set_default_properties()
    {
        $this->set_catalog_visibility('visible');
        $this->set_featured(false);
        $this->set_manage_stock(false);
        $this->set_stock_status('instock');
        $this->set_sold_individually(true);
    }

    /**
     * Get product type
     * 
     * @return string
     */
    public function get_type()
    {
        return 'bsa-gift-card';
    }

    /**
     * Check if product is purchasable
     * 
     * @return bool
     */
    public function is_purchasable()
    {
        return $this->exists() &&
            $this->get_status() === 'publish' &&
            $this->is_in_stock();
    }

    /**
     * Gift cards don't need stock management
     * 
     * @return bool
     */
    public function managing_stock()
    {
        return false;
    }

    /**
     * Gift cards are always in stock
     * 
     * @return bool
     */
    public function is_in_stock()
    {
        return true;
    }

    /**
     * Get stock status
     * 
     * @param string $context View context
     * @return string
     */
    public function get_stock_status($context = 'view')
    {
        return 'instock';
    }

    /**
     * Gift cards are sold individually
     * 
     * @return bool
     */
    public function is_sold_individually()
    {
        return true;
    }

    /**
     * Check if virtual based on stored meta
     * 
     * @return bool
     */
    public function is_virtual()
    {
        // Check product meta first
        $virtual = $this->get_meta('_virtual', true);
        if ($virtual === 'yes') {
            return true;
        }

        // Fallback to cart context logic
        if (WC()->cart && !is_admin()) {
            foreach (WC()->cart->get_cart() as $cart_item) {
                if (
                    isset($cart_item['gift_card_format']) &&
                    $cart_item['data']->get_id() === $this->get_id()
                ) {
                    return $cart_item['gift_card_format'] === 'digital';
                }
            }
        }

        return false;
    }

    /**
     * Check if downloadable based on stored meta
     * 
     * @return bool  
     */
    public function is_downloadable()
    {
        return $this->get_meta('_downloadable', true) === 'yes';
    }


    /**
     * Check if needs shipping (based on format)
     * 
     * @return bool
     */
    public function needs_shipping()
    {
        // In cart context, check for format-specific shipping needs
        if (WC()->cart && !is_admin()) {
            foreach (WC()->cart->get_cart() as $cart_item) {
                if (
                    isset($cart_item['data']) &&
                    $cart_item['data']->get_id() === $this->get_id() &&
                    isset($cart_item['gift_card_format'])
                ) {

                    return $cart_item['gift_card_format'] === 'print';
                }
            }
        }

        // Default to no shipping
        return false;
    }

    /**
     * Gift cards are typically not taxable (can be overridden)
     * 
     * @return bool
     */
    public function is_taxable()
    {
        return apply_filters('bsa_gift_card_is_taxable', false, $this);
    }

    /**
     * Get add to cart button text
     * 
     * @return string
     */
    public function add_to_cart_text()
    {
        return __('Geschenkgutschein kaufen', 'bsa-gift-card');
    }

    /**
     * Get single add to cart button text
     * 
     * @return string
     */
    public function single_add_to_cart_text()
    {
        return __('Geschenkgutschein kaufen', 'bsa-gift-card');
    }

    /**
     * Get price HTML (empty since price is custom)
     * 
     * @param string $price_html Price HTML
     * @return string
     */
    public function get_price_html($price_html = '')
    {
        // Check if shop/archive pages
        if (is_shop() || is_product_category() || is_product_tag() || wc_get_loop_prop('is_shortcode')) {
            return '';
        }
    }

    /**
     * Check if product has weight
     * 
     * @return bool
     */
    public function has_weight()
    {
        return !$this->is_virtual();
    }

    /**
     * Get weight (for shipping calculation)
     * 
     * @return float
     */
    public function get_weight($context = 'view')
    {
        // Return small weight for print format in cart context
        if (!$this->is_virtual()) {
            return 0.1;
        }

        return 0;
    }
}
