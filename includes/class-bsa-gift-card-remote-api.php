<?php
if (!defined('ABSPATH')) exit;

/**
 * BSA Gift Card Remote API
 * 
 * Handles communication with the remote WooCommerce store
 */
class BSA_Gift_Card_Remote_API
{
    /**
     * API settings
     */
    protected $settings;

    /**
     * Logger instance
     */
    protected $logger;

    /**
     * Constructor
     */
    public function __construct()
    {
        // This option name must match the one in the settings class
        $this->settings = get_option('bsa_gift_card_api_settings', []);
        if (class_exists('BSA_Gift_Card_Logger')) {
            $this->logger = BSA_Gift_Card_Logger::get_instance();
        }
    }

    /**
     * Get API settings for external use
     * 
     * @return array API settings
     */
    public function get_api_settings()
    {
        return $this->settings;
    }

    /**
     * Check if remote API is configured and active
     * 
     * @return bool True if remote API is configured
     */
    public function is_remote_api_active()
    {
        return !empty($this->settings['remote_url']) && 
               !empty($this->settings['consumer_key']) && 
               !empty($this->settings['consumer_secret']);
    }

    /**
     * Centralized method for making API requests to the remote server.
     *
     * @param string $endpoint The API endpoint (e.g., '/wc/v3/coupons').
     * @param array  $params   Optional. Parameters for the request (body, etc.).
     * @param string $method   Optional. HTTP method (GET, POST, etc.). Default 'GET'.
     * @return array|WP_Error The response from wp_remote_request or a WP_Error object.
     */
    public function make_request($endpoint, $params = [], $method = 'GET')
    {
        if (!$this->is_remote_api_active()) {
            return new WP_Error('api_not_configured', __('Remote API ist nicht konfiguriert.', 'bsa-gift-card'));
        }

        $request_url = rtrim($this->settings['remote_url'], '/') . '/wp-json' . $endpoint;
        $auth_header = 'Basic ' . base64_encode($this->settings['consumer_key'] . ':' . $this->settings['consumer_secret']);

        $args = [
            'method'  => $method,
            'headers' => [
                'Authorization' => $auth_header,
                'Content-Type'  => 'application/json',
            ],
            'timeout' => 30,
            'user-agent' => 'Mozilla/5.0 (WordPress)',
        ];

        // Conditionally disable SSL verification based on the plugin setting
        if (isset($this->settings['disable_ssl_verify']) && $this->settings['disable_ssl_verify'] === 'yes') {
            $args['sslverify'] = false;
        }

        if (!empty($params) && (strtoupper($method) === 'POST' || strtoupper($method) === 'PUT')) {
            $args['body'] = wp_json_encode($params);
        }

        return wp_remote_request($request_url, $args);
    }
}
