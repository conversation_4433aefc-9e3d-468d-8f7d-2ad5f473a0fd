<?php
if (!defined('ABSPATH')) exit;

/**
 * BSA Gift Card API Settings Manager
 * 
 * Handles integration registration, backward compatibility, and API connection testing
 */
class BSA_Gift_Card_API_Settings
{
    /**
     * Integration instance
     */
    private $integration;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->register_integration();
        $this->register_ajax_handlers();
    }

    /**
     * Register the integration with WooCommerce
     */
    private function register_integration()
    {
        add_filter('woocommerce_integrations', [$this, 'add_integration']);
    }

    /**
     * Register AJAX handlers
     */
    private function register_ajax_handlers()
    {
        add_action('wp_ajax_bsa_test_api_connection', [$this, 'handle_test_connection']);
    }

    /**
     * Add integration to WooCommerce
     */
    public function add_integration($integrations)
    {
        $integrations[] = 'BSA_Gift_Card_API_Integration';
        return $integrations;
    }

    /**
     * Handle AJAX connection test with auto-activation
     */
    public function handle_test_connection()
    {
        // Security checks
        check_ajax_referer('bsa_test_api_connection', 'security');

        if (!current_user_can('manage_woocommerce')) {
            wp_send_json_error([
                'message' => __('Sie haben keine Berechtigung für diese Aktion.', 'bsa-gift-card')
            ]);
        }

        // Get and validate credentials
        $credentials = $this->get_test_credentials();
        
        if (!$this->validate_test_credentials($credentials)) {
            $this->log_connection_test(false, $credentials['remote_url'], 'Missing credentials', ['error_type' => 'missing_credentials']);
            wp_send_json_error(['message' => __('Bitte füllen Sie alle API-Einstellungen aus.', 'bsa-gift-card')]);
        }

        // Perform connection test
        $test_result = $this->perform_connection_test($credentials);
        
        if ($test_result['success']) {
            $this->handle_successful_connection($credentials);
            wp_send_json_success(['message' => $test_result['message']]);
        } else {
            $this->handle_failed_connection($credentials, $test_result);
            wp_send_json_error(['message' => $test_result['message']]);
        }
    }

    /**
     * Get credentials from POST data
     */
    private function get_test_credentials()
    {
        return [
            'remote_url' => isset($_POST['remote_url']) ? esc_url_raw(rtrim(trim($_POST['remote_url']), '/')) : '',
            'consumer_key' => isset($_POST['consumer_key']) ? sanitize_text_field($_POST['consumer_key']) : '',
            'consumer_secret' => isset($_POST['consumer_secret']) ? sanitize_text_field($_POST['consumer_secret']) : ''
        ];
    }

    /**
     * Validate test credentials
     */
    private function validate_test_credentials($credentials)
    {
        if (empty($credentials['remote_url']) || empty($credentials['consumer_key']) || empty($credentials['consumer_secret'])) {
            return false;
        }

        return filter_var($credentials['remote_url'], FILTER_VALIDATE_URL) !== false;
    }

    /**
     * Perform the actual connection test
     */
    private function perform_connection_test($credentials)
    {
        $api_url = $credentials['remote_url'] . '/wp-json/wc/v3/system_status';

        $response = wp_remote_get($api_url, [
            'headers' => [
                'Authorization' => 'Basic ' . base64_encode($credentials['consumer_key'] . ':' . $credentials['consumer_secret']),
                'Content-Type' => 'application/json',
                'Accept' => 'application/json'
            ],
            'timeout' => apply_filters('bsa_gift_card_api_timeout', 30),
            'sslverify' => apply_filters('bsa_gift_card_api_ssl_verify', false)
        ]);

        if (is_wp_error($response)) {
            return [
                'success' => false,
                'message' => $response->get_error_message(),
                'context' => ['error_type' => 'wp_error', 'endpoint' => $api_url]
            ];
        }

        $response_code = wp_remote_retrieve_response_code($response);
        
        if ($response_code === 200) {
            return [
                'success' => true,
                'message' => __('Verbindung erfolgreich! API wurde automatisch aktiviert für Remote-Gutschein-Erstellung.', 'bsa-gift-card')
            ];
        }

        // Parse error response
        $response_body = wp_remote_retrieve_body($response);
        $error_data = json_decode($response_body, true);
        $error_message = isset($error_data['message']) ? $error_data['message'] : __('Unbekannter Fehler', 'bsa-gift-card');

        return [
            'success' => false,
            'message' => sprintf(__('API Fehler: %s (Code: %d)', 'bsa-gift-card'), $error_message, $response_code),
            'context' => ['response_code' => $response_code, 'endpoint' => $api_url]
        ];
    }

    /**
     * Handle successful connection
     */
    private function handle_successful_connection($credentials)
    {
        // Update connection status
        update_option('woocommerce_bsa_gift_card_api_connection_status', 'api_aktiviert');

        // Update unified API settings with auto-activation
        $api_settings = [
            'enabled' => 'yes', // AUTO-ACTIVATE
            'remote_url' => $credentials['remote_url'],
            'consumer_key' => $credentials['consumer_key'],
            'consumer_secret' => $credentials['consumer_secret'],
            'connection_status' => 'api_aktiviert'
        ];
        
        update_option('bsa_gift_card_api_settings', $api_settings);
        
        $this->log_connection_test(true, $credentials['remote_url'], 'Connection successful - API activated');
    }

    /**
     * Handle failed connection
     */
    private function handle_failed_connection($credentials, $test_result)
    {
        update_option('woocommerce_bsa_gift_card_api_connection_status', 'api_nicht_aktiviert');
        
        $this->log_connection_test(false, $credentials['remote_url'], $test_result['message'], $test_result['context'] ?? []);
    }

    /**
     * Log connection test results
     */
    private function log_connection_test($success, $url, $message, $context = [])
    {
        if (class_exists('BSA_Gift_Card_Logger')) {
            $logger = BSA_Gift_Card_Logger::get_instance();
            $logger->log_api_connection_test($success, $url, $message, $context);
        }
    }

    /**
     * Backward compatibility: Check if API is ready
     */
    public function is_api_ready()
    {
        $settings = get_option('bsa_gift_card_api_settings', []);
        return !empty($settings['enabled']) && 
               $settings['enabled'] === 'yes' &&
               !empty($settings['remote_url']) && 
               !empty($settings['consumer_key']) && 
               !empty($settings['consumer_secret']) &&
               !empty($settings['connection_status']) &&
               $settings['connection_status'] === 'api_aktiviert';
    }

    /**
     * Backward compatibility: Get API settings
     */
    public function get_api_settings()
    {
        return get_option('bsa_gift_card_api_settings', []);
    }

    /**
     * Get API credentials for external use
     */
    public function get_api_credentials()
    {
        $settings = $this->get_api_settings();
        
        if (!$this->is_api_ready()) {
            return false;
        }

        return [
            'remote_url' => $settings['remote_url'],
            'consumer_key' => $settings['consumer_key'],
            'consumer_secret' => $settings['consumer_secret']
        ];
    }
}
