<?php
if (!defined('ABSPATH')) exit;

/**
 * BSA Gift Card API Integration
 * 
 * Handles the WooCommerce Integration UI and settings form
 * Auto-activates when connection test is successful with proper status persistence
 */
class BSA_Gift_Card_API_Integration extends WC_Integration
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->id = 'bsa_gift_card_api';
        $this->method_title = __('BSA Gift Card API', 'bsa-gift-card');
        $this->method_description = __('API-Zugangsdaten für den BSA WooCommerce-Shop zur Remote-Gutschein-Erstellung. Die API wird automatisch aktiviert nach erfolgreichem Verbindungstest.', 'bsa-gift-card');

        // Load the settings
        $this->init_form_fields();
        $this->init_settings();

        // Define user set variables - FIXED: Get from unified settings first
        $unified_settings = get_option('bsa_gift_card_api_settings', []);
        
        $this->remote_url = !empty($unified_settings['remote_url']) ? $unified_settings['remote_url'] : $this->get_option('remote_url');
        $this->consumer_key = !empty($unified_settings['consumer_key']) ? $unified_settings['consumer_key'] : $this->get_option('consumer_key');
        $this->consumer_secret = !empty($unified_settings['consumer_secret']) ? $unified_settings['consumer_secret'] : $this->get_option('consumer_secret');
        $this->connection_status = !empty($unified_settings['connection_status']) ? $unified_settings['connection_status'] : $this->get_option('connection_status', 'nicht_getestet');

        // Actions
        add_action('woocommerce_update_options_integration_' . $this->id, [$this, 'process_admin_options']);
        add_action('woocommerce_update_options_integration_' . $this->id, [$this, 'save_api_settings']);
    }

    /**
     * Initialize integration settings form fields
     */
    public function init_form_fields()
    {
        $this->form_fields = [
            'remote_url' => [
                'title' => __('BSA Shop URL', 'bsa-gift-card'),
                'type' => 'url',
                'description' => __('Vollständige URL des WooCommerce Shop wo Gutscheine erstellt werden sollen.', 'bsa-gift-card'),
                'desc_tip' => true,
                'default' => '',
                'placeholder' => 'https://shop.bsa-akademie.de/',
                'css' => 'min-width:350px;'
            ],
            'consumer_key' => [
                'title' => __('Consumer Key', 'bsa-gift-card'),
                'type' => 'text',
                'description' => __('Consumer Key aus den WooCommerce REST API-Anmeldeinformationen', 'bsa-gift-card'),
                'desc_tip' => true,
                'default' => '',
                'css' => 'min-width:350px;'
            ],
            'consumer_secret' => [
                'title' => __('Consumer Secret', 'bsa-gift-card'),
                'type' => 'password',
                'description' => __('Consumer Secret aus den WooCommerce REST API-Anmeldeinformationen', 'bsa-gift-card'),
                'desc_tip' => true,
                'default' => '',
                'css' => 'min-width:350px;'
            ],
            'connection_status' => [
                'title' => __('Verbindungstest Status', 'bsa-gift-card'),
                'type' => 'bsa_connection_status_display',
                'description' => __('Status der API-Verbindung nach dem letzten Test.', 'bsa-gift-card'),
                'desc_tip' => true,
                'default' => 'nicht_getestet'
            ],
            'test_connection' => [
                'title' => __('Verbindungstest', 'bsa-gift-card'),
                'type' => 'bsa_api_test_connection',
                'description' => __('Verbindungstest mit der remote WooCommerce BSA Shop REST-API. Nach erfolgreichem Test wird die API automatisch aktiviert.', 'bsa-gift-card'),
                'desc_tip' => true
            ]
        ];
    }

    /**
     * FIXED: Process admin options with proper field change detection
     */
    public function process_admin_options()
    {
        // Get unified settings as source of truth
        $unified_settings = get_option('bsa_gift_card_api_settings', []);
        
        // Get old values from unified settings (more reliable)
        $old_values = [
            'remote_url' => !empty($unified_settings['remote_url']) ? $unified_settings['remote_url'] : '',
            'consumer_key' => !empty($unified_settings['consumer_key']) ? $unified_settings['consumer_key'] : '',
            'consumer_secret' => !empty($unified_settings['consumer_secret']) ? $unified_settings['consumer_secret'] : ''
        ];

        // Process the form normally
        $result = parent::process_admin_options();

        // Get new values from POST data (what user actually submitted)
        $new_values = [
            'remote_url' => isset($_POST['woocommerce_bsa_gift_card_api_remote_url']) ? 
                esc_url_raw(rtrim(trim($_POST['woocommerce_bsa_gift_card_api_remote_url']), '/')) : '',
            'consumer_key' => isset($_POST['woocommerce_bsa_gift_card_api_consumer_key']) ? 
                sanitize_text_field($_POST['woocommerce_bsa_gift_card_api_consumer_key']) : '',
            'consumer_secret' => isset($_POST['woocommerce_bsa_gift_card_api_consumer_secret']) ? 
                sanitize_text_field($_POST['woocommerce_bsa_gift_card_api_consumer_secret']) : ''
        ];

        // FIXED: Only reset if there are ACTUAL changes
        $has_changes = false;
        foreach ($old_values as $key => $old_value) {
            if ($old_value !== $new_values[$key]) {
                $has_changes = true;
                break;
            }
        }

        // Only reset connection status if credentials actually changed
        if ($has_changes) {
            $this->reset_connection_status();
            
            // Debug logging
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log('BSA Gift Card: API credentials changed - connection status reset');
                error_log('Old values: ' . print_r($old_values, true));
                error_log('New values: ' . print_r($new_values, true));
            }
        } else {
            // FIXED: Preserve existing connection status when no changes
            if (!empty($unified_settings['connection_status'])) {
                $this->connection_status = $unified_settings['connection_status'];
                $this->update_option('connection_status', $this->connection_status);
            }
        }

        return $result;
    }

    /**
     * Reset connection status when credentials change
     */
    private function reset_connection_status()
    {
        $this->update_option('connection_status', 'nicht_getestet');
        $this->connection_status = 'nicht_getestet';
        
        // Also update unified settings
        $unified_settings = get_option('bsa_gift_card_api_settings', []);
        $unified_settings['connection_status'] = 'nicht_getestet';
        $unified_settings['enabled'] = 'no'; // Disable API when credentials change
        update_option('bsa_gift_card_api_settings', $unified_settings);
    }

    /**
     * Generate connection status display HTML
     */
    public function generate_bsa_connection_status_display_html($key, $data)
    {
        $field_key = $this->get_field_key($key);
        $defaults = [
            'title' => '',
            'class' => '',
            'css' => '',
            'desc_tip' => false,
            'description' => '',
            'default' => 'nicht_getestet'
        ];

        $data = wp_parse_args($data, $defaults);
        
        // FIXED: Get status from unified settings first
        $unified_settings = get_option('bsa_gift_card_api_settings', []);
        $current_status = !empty($unified_settings['connection_status']) ? 
            $unified_settings['connection_status'] : 
            $this->get_option($key, $data['default']);
            
        $status_config = $this->get_status_config($current_status);

        ob_start();
        ?>
        <tr valign="top">
            <th scope="row" class="titledesc">
                <label for="<?php echo esc_attr($field_key); ?>"><?php echo wp_kses_post($data['title']); ?></label>
                <?php echo $this->get_tooltip_html($data); ?>
            </th>
            <td class="forminp">
                <fieldset>
                    <legend class="screen-reader-text"><span><?php echo wp_kses_post($data['title']); ?></span></legend>
                    
                    <input type="hidden" id="<?php echo esc_attr($field_key); ?>" name="<?php echo esc_attr($field_key); ?>" value="<?php echo esc_attr($current_status); ?>" />
                    
                    <span id="<?php echo esc_attr($field_key); ?>_display" class="bsa-status-display" style="
                        color: <?php echo esc_attr($status_config['color']); ?>; 
                        font-weight: bold; 
                        font-size: 14px;
                        padding: 5px 10px;
                        border-radius: 3px;
                        display: inline-block;
                    ">
                        <?php echo esc_html($status_config['icon'] . ' ' . $status_config['text']); ?>
                    </span>
                    
                    <?php echo $this->get_description_html($data); ?>
                </fieldset>
            </td>
        </tr>
        <?php
        return ob_get_clean();
    }

    /**
     * Generate test connection button HTML with JavaScript
     */
    public function generate_bsa_api_test_connection_html($key, $data)
    {
        $field_key = $this->get_field_key($key);
        $defaults = [
            'title' => '',
            'class' => '',
            'css' => '',
            'desc_tip' => false,
            'description' => ''
        ];

        $data = wp_parse_args($data, $defaults);

        ob_start();
        ?>
        <tr valign="top">
            <th scope="row" class="titledesc">
                <label for="<?php echo esc_attr($field_key); ?>"><?php echo wp_kses_post($data['title']); ?></label>
                <?php echo $this->get_tooltip_html($data); ?>
            </th>
            <td class="forminp">
                <fieldset>
                    <legend class="screen-reader-text"><span><?php echo wp_kses_post($data['title']); ?></span></legend>
                    <button type="button" id="bsa_api_test_connection_btn" class="button button-secondary">
                        <?php esc_html_e('Verbindung testen', 'bsa-gift-card'); ?>
                    </button>
                    <span id="bsa_api_test_connection_result" style="margin-left:10px;"></span>
                    <?php echo $this->get_description_html($data); ?>
                </fieldset>
            </td>
        </tr>
        <?php
        
        // Enqueue the JavaScript
        $this->enqueue_connection_test_script();
        
        return ob_get_clean();
    }

    /**
     * FIXED: Enhanced connection test JavaScript with better status persistence
     */
    private function enqueue_connection_test_script()
    {
        static $script_enqueued = false;
        
        if ($script_enqueued) {
            return;
        }
        
        $script_enqueued = true;
        
        // Get current unified settings for JavaScript
        $unified_settings = get_option('bsa_gift_card_api_settings', []);
        
        ?>
        <script type="text/javascript">
        jQuery(document).ready(function($) {
            const BSAConnectionTest = {
                statusConfig: <?php echo wp_json_encode($this->get_all_status_configs()); ?>,
                originalValues: {}, // Store original values from server
                
                init: function() {
                    this.bindEvents();
                    this.storeOriginalValues();
                    this.monitorFieldChanges();
                },
                
                storeOriginalValues: function() {
                    // Store server-side values as original (source of truth)
                    this.originalValues = {
                        remote_url: '<?php echo esc_js($unified_settings['remote_url'] ?? ''); ?>',
                        consumer_key: '<?php echo esc_js($unified_settings['consumer_key'] ?? ''); ?>',
                        consumer_secret: '<?php echo esc_js($unified_settings['consumer_secret'] ?? ''); ?>'
                    };
                },
                
                bindEvents: function() {
                    $('#bsa_api_test_connection_btn').on('click', this.testConnection.bind(this));
                },
                
                monitorFieldChanges: function() {
                    const fieldsToMonitor = [
                        '#woocommerce_bsa_gift_card_api_remote_url',
                        '#woocommerce_bsa_gift_card_api_consumer_key',
                        '#woocommerce_bsa_gift_card_api_consumer_secret'
                    ];
                    
                    fieldsToMonitor.forEach(selector => {
                        $(selector).on('input change', () => {
                            this.checkForChanges();
                        });
                    });
                },
                
                checkForChanges: function() {
                    const currentValues = {
                        remote_url: $('#woocommerce_bsa_gift_card_api_remote_url').val().replace(/\/$/, ''),
                        consumer_key: $('#woocommerce_bsa_gift_card_api_consumer_key').val(),
                        consumer_secret: $('#woocommerce_bsa_gift_card_api_consumer_secret').val()
                    };
                    
                    // Only reset if there are actual changes from server values
                    let hasChanges = false;
                    for (let key in this.originalValues) {
                        if (currentValues[key] !== this.originalValues[key]) {
                            hasChanges = true;
                            break;
                        }
                    }
                    
                    if (hasChanges) {
                        this.updateStatusDisplay('nicht_getestet');
                    }
                },
                
                updateStatusDisplay: function(status) {
                    const statusInfo = this.statusConfig[status] || this.statusConfig['nicht_getestet'];
                    const displayElement = $('#woocommerce_bsa_gift_card_api_connection_status_display');
                    const hiddenInput = $('#woocommerce_bsa_gift_card_api_connection_status');
                    
                    hiddenInput.val(status);
                    displayElement.html(statusInfo.icon + ' ' + statusInfo.text);
                    displayElement.css('color', statusInfo.color);
                },
                
                testConnection: function(e) {
                    e.preventDefault();
                    
                    const resultElement = $('#bsa_api_test_connection_result');
                    const credentials = this.getCredentials();
                    
                    if (!this.validateCredentials(credentials)) {
                        resultElement.html('<span style="color:red;">✗ <?php esc_html_e('Bitte füllen Sie alle Felder aus.', 'bsa-gift-card'); ?></span>');
                        return;
                    }
                    
                    this.performAjaxTest(credentials, resultElement);
                },
                
                getCredentials: function() {
                    return {
                        remote_url: $('#woocommerce_bsa_gift_card_api_remote_url').val(),
                        consumer_key: $('#woocommerce_bsa_gift_card_api_consumer_key').val(),
                        consumer_secret: $('#woocommerce_bsa_gift_card_api_consumer_secret').val()
                    };
                },
                
                validateCredentials: function(credentials) {
                    return credentials.remote_url && credentials.consumer_key && credentials.consumer_secret;
                },
                
                performAjaxTest: function(credentials, resultElement) {
                    resultElement.html('<span style="color:blue;"><?php esc_html_e('Verbindung wird getestet...', 'bsa-gift-card'); ?></span>');
                    
                    $.ajax({
                        url: ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'bsa_test_api_connection',
                            security: '<?php echo wp_create_nonce('bsa_test_api_connection'); ?>',
                            ...credentials
                        },
                        success: (response) => {
                            if (response.success) {
                                resultElement.html('<span style="color:green;">✓ ' + response.data.message + '</span>');
                                this.updateStatusDisplay('api_aktiviert');
                                // Update original values after successful test
                                this.originalValues = {
                                    remote_url: credentials.remote_url.replace(/\/$/, ''),
                                    consumer_key: credentials.consumer_key,
                                    consumer_secret: credentials.consumer_secret
                                };
                            } else {
                                resultElement.html('<span style="color:red;">✗ ' + response.data.message + '</span>');
                                this.updateStatusDisplay('api_nicht_aktiviert');
                            }
                        },
                        error: () => {
                            resultElement.html('<span style="color:red;">✗ <?php esc_html_e('Fehler beim Verbinden zum Server.', 'bsa-gift-card'); ?></span>');
                            this.updateStatusDisplay('api_nicht_aktiviert');
                        }
                    });
                }
            };
            
            BSAConnectionTest.init();
        });
        </script>
        <?php
    }

    /**
     * Get status configuration for display
     */
    private function get_status_config($status)
    {
        $configs = $this->get_all_status_configs();
        return $configs[$status] ?? $configs['nicht_getestet'];
    }

    /**
     * Get all status configurations
     */
    private function get_all_status_configs()
    {
        return [
            'nicht_getestet' => [
                'text' => __('Nicht getestet', 'bsa-gift-card'),
                'color' => '#666',
                'icon' => '○'
            ],
            'api_aktiviert' => [
                'text' => __('API aktiviert', 'bsa-gift-card'),
                'color' => '#46b450',
                'icon' => '✓'
            ],
            'api_nicht_aktiviert' => [
                'text' => __('API nicht aktiviert', 'bsa-gift-card'),
                'color' => '#d63638',
                'icon' => '✗'
            ]
        ];
    }

    /**
     * FIXED: Save API settings with proper status preservation
     */
    public function save_api_settings()
    {
        $is_configured = $this->is_configured();
        $is_tested = ($this->connection_status === 'api_aktiviert');
        $api_enabled = ($is_configured && $is_tested) ? 'yes' : 'no';

        $api_settings = [
            'enabled' => $api_enabled,
            'remote_url' => $this->remote_url,
            'consumer_key' => $this->consumer_key,
            'consumer_secret' => $this->consumer_secret,
            'connection_status' => $this->connection_status
        ];

        // Save unified settings
        update_option('bsa_gift_card_api_settings', $api_settings);

        // Maintain backward compatibility
        $this->save_individual_options();
    }

    /**
     * Save individual options for backward compatibility
     */
    private function save_individual_options()
    {
        update_option('bsa_api_remote_url', $this->remote_url);
        update_option('bsa_api_consumer_key', $this->consumer_key);
        update_option('bsa_api_consumer_secret', $this->consumer_secret);
        update_option('bsa_api_connection_status', $this->connection_status);
    }

    /**
     * Check if API credentials are configured
     */
    private function is_configured()
    {
        return !empty($this->remote_url) && 
               !empty($this->consumer_key) && 
               !empty($this->consumer_secret);
    }

    /**
     * Check if API is ready for use
     */
    public function is_api_ready()
    {
        return $this->is_configured() && ($this->connection_status === 'api_aktiviert');
    }

    /**
     * Admin options display
     */
    public function admin_options()
    {
        ?>
        <h3><?php esc_html_e('BSA Gift Card API', 'bsa-gift-card'); ?></h3>
        <p><?php esc_html_e('Konfigurieren Sie die Verbindung zum BSA WooCommerce-Shop für die Remote-Gutschein-Erstellung.', 'bsa-gift-card'); ?></p>

        <table class="form-table">
            <?php $this->generate_settings_html(); ?>
        </table>
        <?php
    }
}
