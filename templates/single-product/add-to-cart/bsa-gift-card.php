<?php if (!defined('ABSPATH')) exit; ?>

<form class="cart" method="post" enctype="multipart/form-data">

    <div class="gift-card__section gift-card__price">
        <label class="gift-card__label" for="giftCardPrice">
            Betrag<span class="required">*</span>
        </label>
        <div class="gift-card__price--input">
            <input
                type="number"
                class="gift-card__input gift-card__input--price"
                id="gift_card_price"
                name="gift_card_price"
                min="10"
                step="1"
                placeholder="0"
                required>
            <span class="gift-card__input--price">EUR</span>
        </div>
    </div>

    <div class="gift-card__section gift-card__sender">
        <label class="gift-card__label" for="giftCardFrom">
            Von<span class="required">*</span>
        </label>
        <input
            type="text"
            class="gift-card__input"
            id="giftCardFrom"
            name="gift_card_from_name"
            placeholder="<PERSON><PERSON><PERSON><PERSON> den Absendernamen ein"
            required>
    </div>

    <div class="gift-card__section gift-card__sender">
        <label class="gift-card__label" for="giftCardToName">
            Für<span class="required">*</span>
        </label>
        <input
            type="text"
            class="gift-card__input"
            id="giftCardToName"
            name="gift_card_to_name"
            placeholder="Geben Sie den Empfängernamen ein">
    </div>

    <div class="gift-card__section gift-card__message">
        <label class="gift-card__label" for="giftCardMessage">
            Geschenknachricht
            <small style="text-transform: lowercase;">Die maximale Zeichenanzahl liegt bei 100</small>
        </label>
        <textarea
            class="gift-card__textarea"
            id="giftCardMessage"
            name="gift_card_message"
            placeholder="Geben Sie eine Nachricht ein"></textarea>
    </div>

    <div class="gift-card__section gift-card__designs">
        <label class="gift-card__label">Motivauswahl</label>
        <div class="gift-card__design-grid">
            <div class="gift-card__design-item">
                <input type="radio" id="design1" name="gift_card_template" value="Freude schenken" required checked>
                <label for="design1">
                    <img src="<?php echo BSA_GIFT_CARD_PLUGIN_URL; ?>assets/images/bsa_geschenkgutschein.png" alt="Freude schenken" class="gift-card__design-image">
                </label>
            </div>

            <div class="gift-card__design-item">
                <input type="radio" id="design2" name="gift_card_template" value="Geburtstag" required>
                <label for="design2">
                    <img src="<?php echo BSA_GIFT_CARD_PLUGIN_URL; ?>assets/images/bsa_geschenkgutschein_geburtstag.png" alt="Geburtstag" class="gift-card__design-image">
                </label>
            </div>

            <div class="gift-card__design-item">
                <input type="radio" id="design3" name="gift_card_template" value="Ostern" required>
                <label for="design3">
                    <img src="<?php echo BSA_GIFT_CARD_PLUGIN_URL; ?>assets/images/bsa_geschenkgutschein_ostern.png" alt="Ostern" class="gift-card__design-image">
                </label>
            </div>

            <div class="gift-card__design-item">
                <input type="radio" id="design4" name="gift_card_template" value="Weihnachten" required>
                <label for="design4">
                    <img src="<?php echo BSA_GIFT_CARD_PLUGIN_URL; ?>assets/images/bsa_geschenkgutschein_weihnachten.png" alt="Weihnachten" class="gift-card__design-image">
                </label>
            </div>
        </div>
    </div>

    <div class="gift-card__section gift-card__format">
        <div class="gift-card__format-options">
            <div class="gift-card__format-item">
                <label class="gift-card__radio">
                    <input type="radio" name="gift_card_format" value="print" required>
                    <span>Print</span>
                    <small class="gift-card__note gift-card__note--print">zzgl. <a href="#">Versandkosten</a></small>
                </label>
            </div>

            <div class="gift-card__format-item">
                <label class="gift-card__radio">
                    <input type="radio" name="gift_card_format" value="digital" required>
                    <span>Digital</span>
                </label>
                <input
                    type="email"
                    class="gift-card__input"
                    id="giftCardToEmail"
                    name="gift_card_to_email"
                    placeholder="Geben Sie die E-Mail-Adresse ein">
                <small class="gift-card__note gift-card__note--digital">Wir senden ein PDF mit Gutscheincode an die angegebene E-Mail-Adresse</small>
            </div>
        </div>
    </div>


    <!-- Quantity input -->
    <?php
    woocommerce_quantity_input(
        array(
            'min_value'   => apply_filters('woocommerce_quantity_input_min', $product->get_min_purchase_quantity(), $product),
            'max_value'   => apply_filters('woocommerce_quantity_input_max', $product->get_max_purchase_quantity(), $product),
            'input_value' => isset($_POST['quantity']) ? wc_stock_amount(wp_unslash($_POST['quantity'])) : $product->get_min_purchase_quantity(),
        )
    );
    ?>

    <button type="submit" name="add-to-cart" value="<?php echo esc_attr($product->get_id()); ?>" class="gift_card__add_to_cart">
        <?php echo esc_html($product->single_add_to_cart_text()); ?>
    </button>
</form>