<?php
/*
Plugin Name: [DHFPG Shop] BSA Geschenkgutschein
Description: Adds BSA-Geschenkgutschein product to DHFPG WooCommerce Shop with remote coupon creation.
Version: 1.0.10
Author: Mariusz
Text Domain: bsa-gift-card
Domain Path: /languages
Requires PHP: 7.4
Requires at least: 5.0
Tested up to: 6.4
Requires Plugins: WooCommerce
Requires WooCommerce: 6.0
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit('Direct access forbidden.');
}

// Define plugin constants
define('BSA_GIFT_CARD_VERSION', '1.0.8');
define('BSA_GIFT_CARD_PLUGIN_FILE', __FILE__);
define('BSA_GIFT_CARD_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BSA_GIFT_CARD_PLUGIN_URL', plugin_dir_url(__FILE__));
define('BSA_GIFT_CARD_MIN_PHP', '7.4');
define('BSA_GIFT_CARD_MIN_WP', '5.0');
define('BSA_GIFT_CARD_MIN_WC', '6.0');

// Backwards compatibility for existing code
define('BSA_GIFT_PRODUCT_PATH', BSA_GIFT_CARD_PLUGIN_DIR);
define('BSA_GIFT_PRODUCT_URL', BSA_GIFT_CARD_PLUGIN_URL);

/**
 * Main plugin class
 */
final class BSA_Gift_Card_Plugin
{
    /**
     * Single instance of the class
     */
    private static $instance = null;

    /**
     * Get single instance
     */
    public static function instance()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks()
    {
        register_activation_hook(__FILE__, [$this, 'activate']);
        register_deactivation_hook(__FILE__, [$this, 'deactivate']);

        add_action('plugins_loaded', [$this, 'init'], 10);
        add_action('init', [$this, 'load_textdomain']);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_global_assets']);
    }

    /**
     * Plugin activation
     */
    public function activate()
    {
        // Check requirements
        if (!$this->check_requirements()) {
            wp_die(__('BSA Gift Card: Plugin requirements not met.', 'bsa-gift-card'));
        }

        // Create necessary database tables or options
        $this->create_plugin_data();

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin deactivation
     */
    public function deactivate()
    {
        // Cleanup if needed
        flush_rewrite_rules();
    }

    /**
     * Check plugin requirements
     */
    private function check_requirements()
    {
        // Check PHP version
        if (version_compare(PHP_VERSION, BSA_GIFT_CARD_MIN_PHP, '<')) {
            return false;
        }

        // Check WordPress version
        if (version_compare(get_bloginfo('version'), BSA_GIFT_CARD_MIN_WP, '<')) {
            return false;
        }

        // Check WooCommerce
        if (!class_exists('WooCommerce')) {
            return false;
        }

        return true;
    }

    /**
     * Initialize plugin
     */
    public function init()
    {
        if (!$this->check_requirements()) {
            add_action('admin_notices', [$this, 'requirements_notice']);
            return;
        }

        $this->include_files();
        $this->init_classes();
        $this->init_filters();
    }

    /**
     * Show requirements notice
     */
    public function requirements_notice()
    {
?>
        <div class="error">
            <p>
                <?php
                printf(
                    __('BSA Gift Card requires PHP %s+, WordPress %s+, and WooCommerce %s+ to function properly.', 'bsa-gift-card'),
                    BSA_GIFT_CARD_MIN_PHP,
                    BSA_GIFT_CARD_MIN_WP,
                    BSA_GIFT_CARD_MIN_WC
                );
                ?>
            </p>
        </div>
<?php
    }

    /**
     * Include required files
     */
    private function include_files()
    {
        $includes = [
            'includes/class-bsa-gift-card.php',
            'includes/class-bsa-gift-card-logger.php',
            'includes/class-bsa-gift-card-coupon-generator.php',
            'includes/class-bsa-gift-card-api-settings.php',
            'includes/class-bsa-gift-card-api-integration.php',
            'includes/class-wc-product-bsa-gift-card.php',
            'includes/class-bsa-gift-card-pdf-generator.php'
        ];

        // Include required files
        foreach ($includes as $file) {
            $filepath = BSA_GIFT_CARD_PLUGIN_DIR . $file;
            if (file_exists($filepath)) {
                require_once $filepath;
            } else {
                wp_die(sprintf(__('BSA Gift Card: Required file %s not found.', 'bsa-gift-card'), $file));
            }
        }
    }


    /**
     * Initialize classes
     */
    private function init_classes()
    {
        try {
            // Initialize logger first
            if (class_exists('BSA_Gift_Card_Logger')) {
                BSA_Gift_Card_Logger::get_instance();
            }

            // Initialize main class
            if (class_exists('BSA_Gift_Card')) {
                $gift_card = new BSA_Gift_Card();
                $gift_card->init();
            }

            // Initialize coupon generator
            if (class_exists('BSA_Gift_Card_Coupon_Generator')) {
                new BSA_Gift_Card_Coupon_Generator();
            }

            // Initialize admin settings
            if (is_admin() && class_exists('BSA_Gift_Card_API_Settings')) {
                new BSA_Gift_Card_API_Settings();
            }
        } catch (Exception $e) {
            add_action('admin_notices', function () use ($e) {
                echo '<div class="error"><p>BSA Gift Card Error: ' . esc_html($e->getMessage()) . '</p></div>';
            });
        }
    }

    /**
     * Initialize filters
     */
    private function init_filters()
    {
        add_filter('woocommerce_locate_template', [$this, 'locate_template'], 10, 3);
        add_action('wp_enqueue_scripts', [$this, 'enqueue_product_scripts']);
    }

    /**
     * Load text domain
     */
    public function load_textdomain()
    {
        load_plugin_textdomain(
            'bsa-gift-card',
            false,
            dirname(plugin_basename(__FILE__)) . '/languages'
        );
    }

    /**
     * Enqueue global assets
     */
    public function enqueue_global_assets()
    {
        wp_enqueue_style(
            'bsa-gift-card-style',
            BSA_GIFT_CARD_PLUGIN_URL . 'assets/css/bsa-gift-card.css',
            [],
            BSA_GIFT_CARD_VERSION
        );
    }

    /**
     * Enqueue product-specific scripts
     */
    public function enqueue_product_scripts()
    {
        if (is_product() && !is_admin()) {
            wp_enqueue_script(
                'bsa-gift-card-frontend',
                BSA_GIFT_CARD_PLUGIN_URL . 'assets/js/bsa-gift-card.js',
                ['jquery'],
                BSA_GIFT_CARD_VERSION,
                true
            );

            wp_localize_script('bsa-gift-card-frontend', 'bsaGiftCard', [
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('bsa-gift-card-nonce'),
                'debug' => defined('WP_DEBUG') && WP_DEBUG
            ]);
        }
    }

    /**
     * Locate custom templates
     */
    public function locate_template($template, $template_name, $template_path)
    {
        if ($template_name === 'single-product/add-to-cart/bsa-gift-card.php') {
            $plugin_template = BSA_GIFT_CARD_PLUGIN_DIR . 'templates/' . $template_name;
            if (file_exists($plugin_template)) {
                return $plugin_template;
            }
        }
        return $template;
    }

    /**
     * Create plugin data on activation
     */
    private function create_plugin_data()
    {
        // Set default options
        add_option('bsa_gift_card_version', BSA_GIFT_CARD_VERSION);
        add_option('bsa_gift_card_api_settings', []);
    }
}

// Initialize the plugin
BSA_Gift_Card_Plugin::instance();
