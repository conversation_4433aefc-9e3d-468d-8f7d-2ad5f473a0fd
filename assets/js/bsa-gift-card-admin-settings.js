jQuery(document).ready(function($) {
    // Format the remote URL when saving
    $('form').on('submit', function() {
        const remoteUrl = $('#bsa_gift_card_remote_url').val().trim();
        if (remoteUrl && !remoteUrl.endsWith('/')) {
            $('#bsa_gift_card_remote_url').val(remoteUrl + '/');
        }
    });
    
    // Add copy button for coupon codes in order meta box
    $('.bsa-coupon-code-copy').on('click', function(e) {
        e.preventDefault();
        
        const couponCode = $(this).data('coupon');
        navigator.clipboard.writeText(couponCode).then(() => {
            // Show "Copied!" text
            const originalText = $(this).text();
            $(this).text('Copied!');
            
            // Restore original text after 2 seconds
            setTimeout(() => {
                $(this).text(originalText);
            }, 2000);
        });
    });
});
