jQuery(document).ready(function ($) {
    'use strict';
    
    console.log('BSA Gift Card: Complete JS loaded');
    
    function initGiftCardForm() {
        console.log('BSA Gift Card: Initializing form...');
        
        const $form = $('.cart');
        if (!$form.length) {
            console.log('BSA Gift Card: No cart form found');
            return;
        }
        
        console.log('BSA Gift Card: Form found, setting up interactions...');
        
        // ===== INITIALIZE DEFAULT STATES =====
        initializeDefaultStates();
        
        // ===== DESIGN SELECTION =====
        setupDesignSelection();
        
        // ===== FORMAT SELECTION =====
        setupFormatSelection();
        
        // ===== PRICE INPUT EFFECTS =====
        setupPriceInputEffects();
        
        // ===== FORM VALIDATION =====
        setupFormValidation();
        
        console.log('BSA Gift Card: Initialization complete');
    }
    
    function initializeDefaultStates() {
        console.log('BSA Gift Card: Setting default states...');
        
        // DISABLE ALL FORMAT INPUTS BY DEFAULT
        $('#giftCardToEmail').prop('disabled', true).prop('required', false);
        
        // Remove active class from all format items
        $('.gift-card__format-item').removeClass('active');
        
        // Hide all format notes
        $('.gift-card__note').hide();
        
        // SET DEFAULT DESIGN (design1) WITH RED BORDER
        const $defaultDesign = $('#design1');
        if ($defaultDesign.length) {
            $defaultDesign.prop('checked', true);
            handleDesignSelection($defaultDesign);
        }
        
        console.log('Default states set - inputs disabled, design1 selected');
    }
    
    function setupDesignSelection() {
        console.log('BSA Gift Card: Setting up design selection...');
        
        // handle design radio button changes
        $('input[name="gift_card_template"]').on('change', function() {
            console.log('Design changed to:', $(this).val());
            handleDesignSelection($(this));
        });
        
        // handle clicks on design images
        $('.gift-card__design-image').on('click', function(e) {
            e.preventDefault();
            const $radio = $(this).closest('.gift-card__design-item').find('input[type="radio"]');
            if ($radio.length) {
                $radio.prop('checked', true).trigger('change');
            }
        });
        
        // handle clicks on design labels
        $('.gift-card__design-item label').on('click', function(e) {
            const $radio = $(this).closest('.gift-card__design-item').find('input[type="radio"]');
            if ($radio.length) {
                $radio.prop('checked', true).trigger('change');
            }
        });
    }
    
    function handleDesignSelection($selectedRadio) {
        // remove red border from all design images
        $('.gift-card__design-image').removeClass('selected').css({
            'border': '2px solid transparent',
            'box-shadow': 'none'
        });
        
        // add red border to selected design
        const $selectedImage = $selectedRadio.closest('.gift-card__design-item').find('.gift-card__design-image');
        $selectedImage.addClass('selected').css({
            'border': '2px solid #C4281F',
            'box-shadow': '0 0 10px rgba(196, 40, 31, 0.3)'
        });
        
        // change main product image
        const newImageSrc = $selectedImage.attr('src');
        if (newImageSrc) {
            changeMainProductImage(newImageSrc);
        }
        
        console.log('Design selection updated with red border');
    }
    
    function changeMainProductImage(newImageSrc) {
        if (!newImageSrc) return;
        
        // different selectors for the main product image
        const selectors = [
            '.woocommerce-product-gallery__wrapper img',
            '.woocommerce-product-gallery__image img',
            '.product-gallery img',
            '.product-image img',
            '.elementor-image img',
            '.wp-post-image'
        ];
        
        let imageChanged = false;
        for (let selector of selectors) {
            const $mainImage = $(selector).first();
            if ($mainImage.length) {
                console.log('Changing main product image to:', newImageSrc);
                $mainImage.attr('src', newImageSrc);
                $mainImage.removeAttr('srcset'); // Remove srcset to force new image
                imageChanged = true;
                break;
            }
        }
        
        if (!imageChanged) {
            console.log('Main product image container not found');
        }
    }
    
    function setupFormatSelection() {
        console.log('BSA Gift Card: Setting up format selection...');
        
        // Handle format selection changes
        $('input[name="gift_card_format"]').on('change', function() {
            const format = $(this).val();
            console.log('Format changed to:', format);
            handleFormatChange(format);
        });
    }
    
    function handleFormatChange(format) {
        const $printItem = $('.gift-card__format-item').eq(0);
        const $digitalItem = $('.gift-card__format-item').eq(1);
        const $emailInput = $('#giftCardToEmail');
        
        console.log('Handling format change to:', format);
        
        // Reset all format items
        $('.gift-card__format-item').removeClass('active');
        $('.gift-card__note').hide();
        
        // Disable all inputs first and clear values
        $emailInput.prop('disabled', true).prop('required', false).val('');
        
        if (format === 'print') {
            console.log('Activating print format');
            $printItem.addClass('active');
            $('.gift-card__note--print').show();
            
        } else if (format === 'digital') {
            console.log('Activating digital format');
            $digitalItem.addClass('active');
            $emailInput.prop('disabled', false).prop('required', true);
            $('.gift-card__note--digital').show();
        }
        
        // Log current state for debugging
        console.log('Email input - disabled:', $emailInput.prop('disabled'), 'required:', $emailInput.prop('required'));
    }
    
    function setupPriceInputEffects() {
        const $priceInput = $('#gift_card_price');
        const $priceContainer = $('.gift-card__price--input');
        
        if ($priceInput.length && $priceContainer.length) {
            $priceInput.on('focus', function() {
                $priceContainer.addClass('gift-card__price--input-active');
            }).on('blur', function() {
                $priceContainer.removeClass('gift-card__price--input-active');
            });
            
            // Price formatting and validation
            $priceInput.on('input', function() {
                const price = parseFloat($(this).val());
                if (!isNaN(price) && price > 0) {
                    console.log('Price updated to: €' + price.toFixed(2));
                }
            });
        }
    }
    
    function setupFormValidation() {
        $('.cart').on('submit', function(e) {
            console.log('Form submission started');
            
            if (!validateForm()) {
                e.preventDefault();
                console.log('Form validation failed - submission blocked');
                return false;
            }
            
            console.log('Form validation passed');
        });
    }
    
    function validateForm() {
        let isValid = true;
        const errors = [];
        
        // Validate price
        const price = parseFloat($('#gift_card_price').val());
        if (!price || price < 10) {
            errors.push('Bitte geben Sie einen Betrag von mindestens 10€ ein.');
            isValid = false;
        }
        
        // Validate sender name
        if (!$('#giftCardFrom').val().trim()) {
            errors.push('Bitte geben Sie den Absendernamen ein.');
            isValid = false;
        }
        
        // Validate design selection
        if (!$('input[name="gift_card_template"]:checked').val()) {
            errors.push('Bitte wählen Sie ein Design aus.');
            isValid = false;
        }
        
        // Validate format selection
        const format = $('input[name="gift_card_format"]:checked').val();
        if (!format) {
            errors.push('Bitte wählen Sie ein Format aus (Print oder Digital).');
            isValid = false;
        }
        
        // Validate format-specific fields
        if (format === 'print') {
            if (!$('#giftCardToName').val().trim()) {
                errors.push('Bitte geben Sie den Empfängernamen ein.');
                isValid = false;
            }
        } else if (format === 'digital') {
            const email = $('#giftCardToEmail').val().trim();
            if (!email) {
                errors.push('Bitte geben Sie die E-Mail-Adresse für den digitalen Versand ein.');
                isValid = false;
            } else if (!isValidEmail(email)) {
                errors.push('Bitte geben Sie eine gültige E-Mail-Adresse ein.');
                isValid = false;
            }
        }
        
        // Show errors if any
        if (!isValid) {
            alert(errors.join('\n'));
            console.log('Validation errors:', errors);
        }
        
        return isValid;
    }
    
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // Initialize immediately
    initGiftCardForm();
    
    // For Elementor compatibility
    if (typeof elementorFrontend !== 'undefined') {
        console.log('BSA Gift Card: Setting up Elementor compatibility');
        $(document).on('elementor/frontend/init', function() {
            elementorFrontend.hooks.addAction('frontend/element_ready/global', function() {
                setTimeout(initGiftCardForm, 100);
            });
        });
    }
    
    // Additional safety initialization for dynamic content
    $(document).on('DOMNodeInserted', '.cart', function() {
        setTimeout(initGiftCardForm, 50);
    });
    
    // Manual re-initialization function (for debugging)
    window.reinitBSAGiftCard = function() {
        console.log('Manual re-initialization called');
        initGiftCardForm();
    };
    
    console.log('BSA Gift Card: Script setup complete');
});
