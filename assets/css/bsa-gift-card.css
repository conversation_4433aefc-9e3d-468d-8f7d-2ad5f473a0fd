:root {
    --primary-color: #C4281F;
    --button-hover: #a01f18;
    --border-color: #999;
    --bg-color: #EEF0F1;
    --text-color: #54595F;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --border-radius: 12px;
}

/* Section Styles */
.gift-card__section {
    margin-bottom: var(--spacing-lg);
}

/* Label Styles */
.gift-card__label {
    display: block;
    text-transform: uppercase;
    font-weight: bold;
    letter-spacing: 1.05px;
    margin-bottom: var(--spacing-sm);
}

.gift-card__label .required {
    color: var(--primary-color);
}

/* Input Styles */
.gift-card__input {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-sm);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-sm);
    box-sizing: border-box;
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

.gift-card__input:focus {
    border-color: #333 !important;
    outline: none !important;
    box-shadow: none !important;
}

.gift-card__input:disabled {
    background-color: #f5f5f5 !important;
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    border-color: #ddd !important;
    color: #999 !important;
}

.gift-card__input:enabled {
    background-color: white;
    opacity: 1;
    border-color: var(--border-color);
}

.gift-card__input:enabled:focus {
    border-color: #333 !important;
    outline: none !important;
}

/* Price Input Specific Styles */
.gift-card__price--input {
    width: 50%;
}

.gift-card__price--input input[type=number] {
    border-style: none !important;
    flex: 1;
    text-align: left;
    outline: none !important;
    box-shadow: none !important;
    margin-top: 0;
}

.gift-card__price--input input[type=number]:hover,
.gift-card__price--input input[type=number]:focus {
    outline: none !important;
    box-shadow: none !important;
    border-color: transparent !important;
}

.gift-card__price--input {
    border: 2px solid transparent;
    box-shadow: 0 0 0 1px var(--border-color);
    display: flex;
    align-items: center;
    padding: 4px 12px;
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.gift-card__price--input-active {
    border-color: #333;
    box-shadow: none;
}

.gift-card__price {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.gift-card__input--price {
    text-align: center;
    font-size: 1.5rem;
    font-weight: bold;
}

/* Textarea Styles */
.gift-card__textarea {
    width: 100%;
    min-height: 100px;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    resize: vertical;
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

.gift-card__textarea:focus {
    border-color: #333 !important;
    outline: none !important;
}

/* Design Grid Styles */
.gift-card__design-grid {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md);
    padding: var(--spacing-sm);
}

/* Design Item Styles */
.gift-card__design-item {
    position: relative;
    flex: 1 1 100px;
    text-align: center;
    transition: all 0.3s ease;
}

.gift-card__design-item.selected {
    transform: scale(1.05);
}

.gift-card__design-image {
    width: 100px;
    height: auto;
    border: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 8px;
    display: block;
}

.gift-card__design-image:hover {
    border-color: var(--primary-color);
    opacity: 0.8;
    transform: scale(1.02);
}

.gift-card__design-image.selected {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 10px rgba(196, 40, 31, 0.3) !important;
}

/* Radio Button Styles */
.gift-card__design-item input[type="radio"] {
    display: none;
}

.gift-card__design-item input[type="radio"]:checked + label img {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 10px rgba(196, 40, 31, 0.3) !important;
}

.gift-card__radio input[type="radio"] {
    appearance: none;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-color);
    transition: 0.2s all linear;
    margin-right: var(--spacing-sm);
    position: relative;
    top: 4px;
}

.gift-card__radio input[type="radio"]:checked {
    border: 6px solid var(--primary-color);
}

/* Submit Button - FIXED VERSION */
.gift_card__add_to_cart,
button.gift_card__add_to_cart {
    background-color: var(--primary-color) !important;
    color: white !important;
    border: none !important;
    padding: 12px 24px !important;
    border-radius: var(--border-radius) !important;
    font-weight: bold !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    width: 100% !important;
    font-size: 16px !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
}
