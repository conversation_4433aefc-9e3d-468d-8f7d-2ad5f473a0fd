# MyPlugin - WooCommerce Cutom Product Type Extension

## Project Description

A WordPress plugin that extends WooCommerce functionality to provide advanced product type

## Core Functionality

- Add Custom Product Type
- REST API endpoints integration for coupon creation on other wordprers e-commerce
- Multi-language support (WPML compatible)

## Plugin File Structure

bsa-gift-card/
├── bsa-gift-card.php # Main plugin
├── assets/
│ ├── css/
│ │ ├── bsa-gift-card-admin.css
│ │ └── bsa-gift-card.css
│ ├── images/
│ │ ├── bsa_geschenkgutschein_geburtstag.png
│ │ ├── bsa_geschenkgutschein_ostern.png
│ │ ├── bsa_geschenkgutschein_weihnachten.png
│ │ └── bsa_geschenkgutschein.png
│ └── js/
│   ├── bsa_gift-card.js
│   └── bsa_gift-card-admin-settings.js
├── docs/
│ └── project.md # This file
├── includes/
├── class-bsa-gift-card-api-integration.php # 
├── class-bsa-gift-card-api-settings.php #  
├── class-bsa-gift-card-coupon-generation.php # 
├── class-bsa-gift-card-logger.php # 
├── class-bsa-gift-card-pdf-generation.php # 
├── class-bsa-gift-card-remote-api.php # 
├── class-bsa-gift-card.php #  
└── class-wc-product-bsa-gift-card.php # 


  ## Dependencies

### WordPress Requirements
- WordPress Version: 6.8+
- PHP Version: 8.3+`
- WooCommerce: 
