# BSA Gift Card - WooCommerce Custom Product Type Extension

## Project Description

A WordPress plugin that extends WooCommerce functionality to provide a custom gift card product type with advanced features including PDF generation, coupon management, and external API integration.

## Core Functionality

- **Custom Product Type**: Adds a specialized gift card product type to WooCommerce
- **REST API Integration**: Provides endpoints for coupon creation and management on external WordPress e-commerce sites
- **PDF Generation**: Automatically generates PDF gift cards with customizable templates
- **Coupon Management**: Creates and manages WooCommerce coupons for gift card redemption
- **Multi-language Support**: Compatible with WPML for internationalization
- **Remote API Communication**: Handles communication with external WordPress installations

## Plugin File Structure

```
bsa-gift-card/
├── bsa-gift-card.php                             # Main plugin file - entry point
├── composer.json                                 # 
├── composer.lock                                 # 
├── assets/                                       # Static assets directory
│   ├── css/                                      # Stylesheets
│   │   ├── bsa-gift-card-admin.css               # Admin interface styles
│   │   └── bsa-gift-card.css                     # Frontend styles
│   ├── images/                                   # Gift card template images
│   │   ├── bsa_geschenkgutschein_geburtstag.png  # Birthday template
│   │   ├── bsa_geschenkgutschein_ostern.png      # Easter template
│   │   ├── bsa_geschenkgutschein_weihnachten.png # Christmas template
│   │   └── bsa_geschenkgutschein.png             # Default template
│   └── js/                                       # JavaScript files
│       ├── bsa_gift-card.js                      # Frontend functionality
│       └── bsa_gift-card-admin-settings.js       # Admin settings interface
├── docs/                                         # Documentation
│   └── project.md                                # Project documentation (this file)
├── includes/                                     # Core PHP classes
    ├── class-bsa-gift-card-api-integration.php   # External API integration handler
    ├── class-bsa-gift-card-api-settings.php      # API configuration and settings
    ├── class-bsa-gift-card-coupon-generation.php # WooCommerce coupon creation logic
    ├── class-bsa-gift-card-logger.php            # Logging and debugging utilities
    ├── class-bsa-gift-card-pdf-generation.php    # PDF gift card generation
    ├── class-bsa-gift-card-remote-api.php        # Remote WordPress API communication
    ├── class-bsa-gift-card.php                   # Main plugin class and orchestration
    └── class-wc-product-bsa-gift-card.php        # Custom WooCommerce product type
├── templates/   
    ├── single-product/
        └── add-to-cart/
             └── bsa-gift-card.php                # woocommerce template for a product site
    ├── bsa_geschenkgutschein_digital_1.pdf       # pdf coupon file to fill with data from a order
    ├── bsa_geschenkgutschein_digital_2.pdf       # pdf coupon file to fill with data from a order
    ├── bsa_geschenkgutschein_digital_3.pdf       # pdf coupon file to fill with data from a order
    ├── bsa_geschenkgutschein_digital_4.pdf       # pdf coupon file to fill with data from a order
    └── bsa_geschenkgutschein_digital.pdf         # pdf coupon file to fill with data from a order
├── vendor/  
    ├── composer/  
        ├── autoload_classmap.php
        ├── autoload_namespaces.php
        ├── autoload_psr4.php
        ├── autoload_real.php
        ├── autoload_static.php
        ├── ClassLoader.php
        ├── installed.json
        ├── installed.php
        ├── installedVersion.php
        ├── LICENSE
        └── platform_check.php
    ├── setasign/  
        ├── src/  
            ├── Math/  
                ├── Matrix.php  
                └── Vector.php  
            ├── PdfParser/  
                ├── CrossReference/  
                ├── Filter/  
                ├── Type/  
                ├── PdfParser.php 
                ├── PdfParserException.php 
                ├── StreamReader.php 
                └── Tokenizer.php 
            ├── PdfReader/  
                ├── DataStructure/  
                    └── Rectangle.php  
                ├── Page.php
                ├── PageBoundaries.php
                ├── PdfReader.php
                └── PdfReaderException.php
            ├── Tcpdf/  
                └── Fpdi.php  
            └── Tfpdf/  
                └── FpdfTpl.php  
                └── Fpdi.php  
        ├── composer.json 
        ├── LICENSE.txt 
        ├── README.md 
        └── SECURITY.md 
    └── autoload.php  
```


## Dependencies

### WordPress Requirements
- **WordPress Version**: 6.8+
- **PHP Version**: 8.3+
- **WooCommerce**: 8.0+ (required for custom product types)

### PHP Extensions Required
- `gd` or `imagick` (for image processing)
- `curl` (for external API communication)
- `json` (for data serialization)

### Optional Dependencies
- **WPML**: For multi-language support
- **WooCommerce PDF Invoices & Packing Slips**: For enhanced PDF functionality

## Key Classes and Their Responsibilities

### Core Classes
- **`BSA_Gift_Card`**: Main plugin orchestrator, handles initialization and hooks
- **`WC_Product_BSA_Gift_Card`**: Custom WooCommerce product type implementation
- **`BSA_Gift_Card_Coupon_Generation`**: Manages WooCommerce coupon creation and validation
- **`BSA_Gift_Card_PDF_Generation`**: Handles PDF gift card creation with templates

### API Classes
- **`BSA_Gift_Card_API_Integration`**: Manages external WordPress site integration
- **`BSA_Gift_Card_Remote_API`**: Handles remote API calls and authentication
- **`BSA_Gift_Card_API_Settings`**: Configuration management for API endpoints

### Utility Classes
- **`BSA_Gift_Card_Logger`**: Centralized logging for debugging and monitoring

## Technical Notes for AI Agents

### Code Architecture
- Plugin follows WordPress coding standards and WooCommerce extension patterns
- Uses object-oriented PHP with proper namespacing
- Implements WordPress hooks and filters for extensibility
- REST API endpoints follow WordPress REST API conventions

### Database Schema
- Utilizes WooCommerce's existing product and order tables
- Custom meta fields store gift card specific data
- Coupon codes are managed through WooCommerce's coupon system

### Security Considerations
- All API endpoints require proper authentication
- Input validation and sanitization implemented throughout
- Nonce verification for admin actions
- Capability checks for user permissions
